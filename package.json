{"name": "coach-me-react", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"dev": "next dev", "dev:backend": "ts-node src/backend/main.ts", "dev:full": "concurrently \"npm run dev:backend\" \"npm run dev\"", "build": "next build", "build:backend": "tsc --project tsconfig.backend.json", "start": "next start", "start:backend": "node dist/backend/main.js", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test && npm run test:e2e"}, "dependencies": {"@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/platform-express": "^11.1.3", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "next": "^14.2.30", "openai": "^5.6.0", "postcss": "^8.5.6", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "tailwindcss": "^4.1.10", "zustand": "^5.0.5"}, "devDependencies": {"@playwright/test": "^1.53.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "concurrently": "^9.2.0", "eslint": "^9.29.0", "eslint-config-next": "^15.3.4", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.5.3"}, "private": true}