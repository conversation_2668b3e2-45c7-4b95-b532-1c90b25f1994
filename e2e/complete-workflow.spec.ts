import { test, expect } from '@playwright/test';

test.describe('Complete Voice Recording and AI Refinement Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Start with a fresh page
    await page.goto('/');
    
    // Grant microphone permissions
    await page.context().grantPermissions(['microphone']);
  });

  test('complete end-to-end workflow: voice recording → transcription → AI refinement', async ({ page }) => {
    // Step 1: Verify initial state
    await expect(page.getByText('Coach Me')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Start recording' })).toBeVisible();
    await expect(page.getByText('Your speech will appear here...')).toBeVisible();

    // Step 2: Mock the voice recording APIs for testing
    await page.route('**/api/refinement/refine', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          refinedPrompt: "I will build a consistent exercise routine by going to the gym every morning.",
          extractedTime: "daily-morning",
          needsTimeCllarification: false,
          confidence: 0.9,
          originalTranscript: "I want to exercise more and go to the gym every morning",
          processingTime: 1200,
          timestamp: new Date().toISOString()
        })
      });
    });

    // Step 3: Simulate voice recording
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    
    // Click to start recording
    await recordingButton.click();
    
    // Verify recording state
    await expect(recordingButton).toHaveClass(/recording/);
    await expect(recordingButton).toHaveAttribute('aria-label', 'Stop recording');
    
    // Verify recording indicator appears
    await expect(page.getByText('Recording')).toBeVisible();
    await expect(page.getByText('Listening...')).toBeVisible();

    // Step 4: Simulate speech recognition result
    await page.evaluate(() => {
      // Simulate speech recognition
      const mockTranscript = "I want to exercise more and go to the gym every morning";
      
      // Update the store directly for testing
      const store = (window as any).__ZUSTAND_STORE__;
      if (store) {
        store.getState().setTranscript(mockTranscript);
      }
    });

    // Verify transcript appears
    await expect(page.getByText('I want to exercise more and go to the gym every morning')).toBeVisible();

    // Step 5: Stop recording
    await recordingButton.click();
    
    // Verify recording stopped
    await expect(recordingButton).toHaveClass(/idle/);
    await expect(recordingButton).toHaveAttribute('aria-label', 'Start recording');

    // Step 6: Wait for AI refinement to trigger
    await page.waitForTimeout(1500); // Wait for auto-refinement delay

    // Step 7: Verify refined prompt appears
    await expect(page.getByText('Refined Coaching Prompt')).toBeVisible();
    await expect(page.getByText('I will build a consistent exercise routine by going to the gym every morning.')).toBeVisible();
    
    // Step 8: Verify extracted time information
    await expect(page.getByText('Extracted Time:')).toBeVisible();
    await expect(page.getByText('daily-morning')).toBeVisible();
    
    // Step 9: Verify confidence and metadata
    await expect(page.getByText('Confidence: 90%')).toBeVisible();
    await expect(page.getByText(/Processed in \d+ms/)).toBeVisible();
    await expect(page.getByText('AI Enhanced')).toBeVisible();
  });

  test('handles microphone permission denial gracefully', async ({ page }) => {
    // Deny microphone permissions
    await page.context().clearPermissions();
    
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await recordingButton.click();
    
    // Should show permission request
    await expect(page.getByText('Microphone Access Required')).toBeVisible();
    await expect(page.getByText('Please allow microphone access to use voice recording features.')).toBeVisible();
    
    // Should have allow access button
    await expect(page.getByRole('button', { name: 'Allow Access' })).toBeVisible();
  });

  test('handles AI service errors gracefully', async ({ page }) => {
    // Mock API error
    await page.route('**/api/refinement/refine', async route => {
      await route.fulfill({
        status: 502,
        contentType: 'application/json',
        body: JSON.stringify({
          message: 'AI service temporarily unavailable',
          code: 'OPENAI_ERROR'
        })
      });
    });

    // Simulate recording and transcript
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await recordingButton.click();
    
    await page.evaluate(() => {
      const mockTranscript = "I want to exercise more";
      const store = (window as any).__ZUSTAND_STORE__;
      if (store) {
        store.getState().setTranscript(mockTranscript);
      }
    });
    
    await recordingButton.click();
    await page.waitForTimeout(1500);
    
    // Should show error message
    await expect(page.getByText('Refinement Error')).toBeVisible();
    await expect(page.getByText(/AI service temporarily unavailable|Failed to connect to refinement service/)).toBeVisible();
  });

  test('time clarification workflow', async ({ page }) => {
    // Mock response that needs time clarification
    await page.route('**/api/refinement/refine', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          refinedPrompt: "I will develop a consistent reading habit.",
          extractedTime: null,
          needsTimeCllarification: true,
          followUpQuestion: "When would you like to work on this goal? Please specify a time or frequency.",
          confidence: 0.7,
          originalTranscript: "I want to read more books",
          processingTime: 800,
          timestamp: new Date().toISOString()
        })
      });
    });

    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await recordingButton.click();
    
    await page.evaluate(() => {
      const mockTranscript = "I want to read more books";
      const store = (window as any).__ZUSTAND_STORE__;
      if (store) {
        store.getState().setTranscript(mockTranscript);
      }
    });
    
    await recordingButton.click();
    await page.waitForTimeout(1500);
    
    // Should show time clarification
    await expect(page.getByText('Time Clarification Needed')).toBeVisible();
    await expect(page.getByText('When would you like to work on this goal? Please specify a time or frequency.')).toBeVisible();
  });

  test('word and character count functionality', async ({ page }) => {
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await recordingButton.click();
    
    const testTranscript = "I want to exercise every morning";
    await page.evaluate((transcript) => {
      const store = (window as any).__ZUSTAND_STORE__;
      if (store) {
        store.getState().setTranscript(transcript);
      }
    }, testTranscript);
    
    // Verify word and character counts
    await expect(page.getByText('Words: 6')).toBeVisible();
    await expect(page.getByText('Characters: 32')).toBeVisible();
  });

  test('error dismissal functionality', async ({ page }) => {
    // Mock API error
    await page.route('**/api/refinement/refine', async route => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          message: 'Invalid request',
          code: 'VALIDATION_ERROR'
        })
      });
    });

    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await recordingButton.click();
    
    await page.evaluate(() => {
      const mockTranscript = "test";
      const store = (window as any).__ZUSTAND_STORE__;
      if (store) {
        store.getState().setTranscript(mockTranscript);
      }
    });
    
    await recordingButton.click();
    await page.waitForTimeout(1500);
    
    // Error should appear
    await expect(page.getByText('Refinement Error')).toBeVisible();
    
    // Click dismiss button
    const dismissButton = page.getByRole('button', { name: 'Dismiss error' });
    await dismissButton.click();
    
    // Error should disappear
    await expect(page.getByText('Refinement Error')).not.toBeVisible();
  });

  test('refined prompt dismissal functionality', async ({ page }) => {
    // Mock successful response
    await page.route('**/api/refinement/refine', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          refinedPrompt: "I will exercise regularly.",
          extractedTime: "daily",
          needsTimeCllarification: false,
          confidence: 0.85,
          originalTranscript: "I want to exercise",
          processingTime: 900,
          timestamp: new Date().toISOString()
        })
      });
    });

    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await recordingButton.click();
    
    await page.evaluate(() => {
      const mockTranscript = "I want to exercise";
      const store = (window as any).__ZUSTAND_STORE__;
      if (store) {
        store.getState().setTranscript(mockTranscript);
      }
    });
    
    await recordingButton.click();
    await page.waitForTimeout(1500);
    
    // Refined prompt should appear
    await expect(page.getByText('Refined Coaching Prompt')).toBeVisible();
    
    // Click clear button
    const clearButton = page.getByRole('button', { name: 'Clear refinement' });
    await clearButton.click();
    
    // Refined prompt should disappear
    await expect(page.getByText('Refined Coaching Prompt')).not.toBeVisible();
  });

  test('mobile responsiveness', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // All main elements should be visible and functional
    await expect(page.getByText('Coach Me')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Start recording' })).toBeVisible();
    await expect(page.getByText('Transcript')).toBeVisible();
    
    // Recording button should be properly sized for touch
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    const buttonBox = await recordingButton.boundingBox();
    expect(buttonBox?.width).toBeGreaterThan(80); // Minimum touch target size
    expect(buttonBox?.height).toBeGreaterThan(80);
  });

  test('keyboard navigation accessibility', async ({ page }) => {
    // Tab through interactive elements
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Recording button should be focused
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await expect(recordingButton).toBeFocused();
    
    // Enter should activate recording
    await page.keyboard.press('Enter');
    await expect(recordingButton).toHaveClass(/recording/);
  });

  test('processing state indicators', async ({ page }) => {
    // Mock slow API response
    await page.route('**/api/refinement/refine', async route => {
      // Delay response to test loading state
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          refinedPrompt: "I will exercise regularly.",
          extractedTime: "daily",
          needsTimeCllarification: false,
          confidence: 0.85,
          originalTranscript: "I want to exercise",
          processingTime: 2000,
          timestamp: new Date().toISOString()
        })
      });
    });

    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await recordingButton.click();
    
    await page.evaluate(() => {
      const mockTranscript = "I want to exercise";
      const store = (window as any).__ZUSTAND_STORE__;
      if (store) {
        store.getState().setTranscript(mockTranscript);
      }
    });
    
    await recordingButton.click();
    await page.waitForTimeout(1500);
    
    // Should show processing indicator
    await expect(page.getByText('Refining your prompt...')).toBeVisible();
    await expect(page.getByText('AI is processing your voice input')).toBeVisible();
    
    // Wait for completion
    await expect(page.getByText('Refined Coaching Prompt')).toBeVisible({ timeout: 10000 });
  });
});
