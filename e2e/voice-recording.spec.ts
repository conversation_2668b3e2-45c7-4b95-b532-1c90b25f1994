import { test, expect } from '@playwright/test';

test.describe('Voice Recording App', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('has correct page title and heading', async ({ page }) => {
    await expect(page).toHaveTitle(/Coach Me/);
    await expect(page.getByRole('heading', { name: '<PERSON> Me' })).toBeVisible();
  });

  test('displays main interface elements', async ({ page }) => {
    // Check for main heading
    await expect(page.getByText('Coach Me')).toBeVisible();
    
    // Check for subtitle
    await expect(page.getByText('Tap the button below to start voice recording')).toBeVisible();
    
    // Check for recording button
    await expect(page.getByRole('button', { name: 'Start recording' })).toBeVisible();
    
    // Check for transcript section
    await expect(page.getByText('Transcript')).toBeVisible();
    await expect(page.getByText('Your speech will appear here...')).toBeVisible();
    
    // Check for instructions
    await expect(page.getByText('How to use:')).toBeVisible();
    
    // Check for browser compatibility note
    await expect(page.getByText('Works best in Chrome, Edge, and Safari browsers')).toBeVisible();
  });

  test('recording button has correct initial state', async ({ page }) => {
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    
    await expect(recordingButton).toBeVisible();
    await expect(recordingButton).toBeEnabled();
    await expect(recordingButton).toHaveClass(/idle/);
  });

  test('shows instructions correctly', async ({ page }) => {
    const instructions = [
      '• Tap the microphone button to start recording',
      '• Speak clearly into your device\'s microphone',
      '• Your speech will be transcribed in real-time',
      '• Tap the button again to stop recording'
    ];

    for (const instruction of instructions) {
      await expect(page.getByText(instruction)).toBeVisible();
    }
  });

  test('transcript display shows placeholder initially', async ({ page }) => {
    await expect(page.getByText('Your speech will appear here...')).toBeVisible();
  });

  test('responsive design works on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that main elements are still visible
    await expect(page.getByText('Coach Me')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Start recording' })).toBeVisible();
    await expect(page.getByText('Transcript')).toBeVisible();
  });

  test('handles browser without speech recognition gracefully', async ({ page, browserName }) => {
    // Skip this test for browsers that support speech recognition
    if (browserName === 'chromium') {
      test.skip();
    }

    // For browsers without speech recognition support
    await page.addInitScript(() => {
      // Remove speech recognition support
      delete (window as any).SpeechRecognition;
      delete (window as any).webkitSpeechRecognition;
    });

    await page.reload();

    // Should show not supported message
    await expect(page.getByText('Browser Not Supported')).toBeVisible();
    await expect(page.getByText(/Voice recording requires a modern browser/)).toBeVisible();
  });

  test('microphone permission request appears when needed', async ({ page, context }) => {
    // Grant microphone permissions
    await context.grantPermissions(['microphone']);
    
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await recordingButton.click();
    
    // The button should change state (though actual recording won't work in test)
    await expect(recordingButton).toHaveAttribute('aria-label', /Stop recording|Processing/);
  });

  test('error display functionality', async ({ page }) => {
    // Mock a permission error by denying microphone access
    await page.route('**/*', route => route.continue());
    
    // Simulate clicking the recording button
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await recordingButton.click();
    
    // Wait a moment for any error to appear
    await page.waitForTimeout(1000);
    
    // Check if error display appears (may not in all test environments)
    const errorDisplay = page.getByText('Recording Error');
    if (await errorDisplay.isVisible()) {
      await expect(errorDisplay).toBeVisible();
    }
  });

  test('keyboard navigation works', async ({ page }) => {
    // Tab to the recording button
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await expect(recordingButton).toBeFocused();
    
    // Press Enter to activate
    await page.keyboard.press('Enter');
    
    // Button should change state
    await expect(recordingButton).toHaveAttribute('aria-label', /Stop recording|Processing/);
  });

  test('visual feedback during recording states', async ({ page, context }) => {
    await context.grantPermissions(['microphone']);
    
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    
    // Initial state
    await expect(recordingButton).toHaveClass(/idle/);
    
    // Click to start recording
    await recordingButton.click();
    
    // Should show processing or recording state
    await expect(recordingButton).toHaveClass(/recording|processing/);
  });

  test('accessibility features', async ({ page }) => {
    // Check for proper ARIA labels
    const recordingButton = page.getByRole('button', { name: 'Start recording' });
    await expect(recordingButton).toHaveAttribute('aria-label', 'Start recording');
    
    // Check for proper heading structure
    await expect(page.getByRole('heading', { level: 1 })).toHaveText('Coach Me');
    await expect(page.getByRole('heading', { level: 3 })).toHaveText('How to use:');
    
    // Check for proper button roles
    await expect(recordingButton).toHaveAttribute('type', 'button');
  });
});
