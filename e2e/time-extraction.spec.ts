import { test, expect } from '@playwright/test';

test.describe('Time Extraction End-to-End Tests', () => {
  const API_BASE = 'http://localhost:3001/api/refinement';

  test('extracts specific times correctly', async ({ request }) => {
    const testCases = [
      {
        text: "I want to exercise at 7:30 AM",
        expectedTime: "07:30",
        expectedType: "specific"
      },
      {
        text: "Let's meet at 2:15 PM",
        expectedTime: "14:15",
        expectedType: "specific"
      },
      {
        text: "I wake up at 6 AM every day",
        expectedTime: "06:00",
        expectedType: "specific"
      },
      {
        text: "Dinner is at 8 PM",
        expectedTime: "20:00",
        expectedType: "specific"
      }
    ];

    for (const testCase of testCases) {
      const response = await request.post(`${API_BASE}/extract-time`, {
        data: { text: testCase.text }
      });

      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data.count).toBeGreaterThan(0);
      
      const specificTimeExtraction = data.extractions.find((e: any) => 
        e.timeType === testCase.expectedType && 
        e.normalizedTime === testCase.expectedTime
      );
      
      expect(specificTimeExtraction).toBeTruthy();
      expect(specificTimeExtraction.confidence).toBeGreaterThan(0.8);
    }
  });

  test('extracts recurring patterns correctly', async ({ request }) => {
    const testCases = [
      {
        text: "I exercise every day",
        expectedPattern: "daily",
        expectedType: "recurring"
      },
      {
        text: "Weekly team meetings",
        expectedPattern: "weekly",
        expectedType: "recurring"
      },
      {
        text: "I read every morning",
        expectedPattern: "daily-morning",
        expectedType: "recurring"
      },
      {
        text: "Gym sessions every Monday",
        expectedPattern: "weekly-monday",
        expectedType: "recurring"
      },
      {
        text: "Monthly reviews",
        expectedPattern: "monthly",
        expectedType: "recurring"
      }
    ];

    for (const testCase of testCases) {
      const response = await request.post(`${API_BASE}/extract-time`, {
        data: { text: testCase.text }
      });

      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data.count).toBeGreaterThan(0);
      
      const recurringExtraction = data.extractions.find((e: any) => 
        e.timeType === testCase.expectedType && 
        e.normalizedTime === testCase.expectedPattern
      );
      
      expect(recurringExtraction).toBeTruthy();
    }
  });

  test('extracts relative times correctly', async ({ request }) => {
    const testCases = [
      {
        text: "I'll start tomorrow",
        expectedPattern: "tomorrow",
        expectedType: "relative"
      },
      {
        text: "Let's do this today",
        expectedPattern: "today",
        expectedType: "relative"
      },
      {
        text: "Next week I'll begin",
        expectedPattern: "next-week",
        expectedType: "relative"
      },
      {
        text: "This morning I'll exercise",
        expectedPattern: "today-morning",
        expectedType: "relative"
      }
    ];

    for (const testCase of testCases) {
      const response = await request.post(`${API_BASE}/extract-time`, {
        data: { text: testCase.text }
      });

      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data.count).toBeGreaterThan(0);
      
      const relativeExtraction = data.extractions.find((e: any) => 
        e.timeType === testCase.expectedType && 
        e.normalizedTime === testCase.expectedPattern
      );
      
      expect(relativeExtraction).toBeTruthy();
    }
  });

  test('handles complex time expressions', async ({ request }) => {
    const complexText = "I want to exercise at 7:30 AM every weekday morning and read for 30 minutes in the evening";
    
    const response = await request.post(`${API_BASE}/extract-time`, {
      data: { text: complexText }
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.count).toBeGreaterThan(2); // Should find multiple time expressions
    
    // Should find specific time
    const hasSpecificTime = data.extractions.some((e: any) => 
      e.timeType === 'specific' && e.timeExpression.includes('7:30 AM')
    );
    expect(hasSpecificTime).toBe(true);
    
    // Should find time of day references
    const hasTimeOfDay = data.extractions.some((e: any) => 
      e.timeExpression.includes('morning') || e.timeExpression.includes('evening')
    );
    expect(hasTimeOfDay).toBe(true);
  });

  test('confidence scoring works correctly', async ({ request }) => {
    const testCases = [
      {
        text: "I exercise at 8:00 AM", // Very specific, should have high confidence
        minConfidence: 0.8
      },
      {
        text: "I exercise every day", // Clear recurring pattern, good confidence
        minConfidence: 0.7
      },
      {
        text: "I exercise sometime in the morning", // Vague, lower confidence
        maxConfidence: 0.7
      }
    ];

    for (const testCase of testCases) {
      const response = await request.post(`${API_BASE}/extract-time`, {
        data: { text: testCase.text }
      });

      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data.count).toBeGreaterThan(0);
      
      if (testCase.minConfidence) {
        expect(data.bestExtraction.confidence).toBeGreaterThanOrEqual(testCase.minConfidence);
      }
      
      if (testCase.maxConfidence) {
        expect(data.bestExtraction.confidence).toBeLessThanOrEqual(testCase.maxConfidence);
      }
    }
  });

  test('best extraction selection works correctly', async ({ request }) => {
    const text = "I want to exercise at 7 AM every day and maybe read sometime";
    
    const response = await request.post(`${API_BASE}/extract-time`, {
      data: { text }
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.count).toBeGreaterThan(1);
    expect(data.bestExtraction).toBeTruthy();
    
    // Best extraction should have the highest confidence
    const allConfidences = data.extractions.map((e: any) => e.confidence);
    const maxConfidence = Math.max(...allConfidences);
    expect(data.bestExtraction.confidence).toBe(maxConfidence);
  });

  test('handles edge cases gracefully', async ({ request }) => {
    const edgeCases = [
      "", // Empty string
      "   ", // Whitespace only
      "No time expressions here", // No time expressions
      "12:60 AM", // Invalid time
      "25:00", // Invalid hour
    ];

    for (const text of edgeCases) {
      const response = await request.post(`${API_BASE}/extract-time`, {
        data: { text }
      });

      if (text.trim() === "") {
        expect(response.status()).toBe(400); // Should reject empty text
      } else {
        expect(response.status()).toBe(200);
        
        const data = await response.json();
        expect(data).toHaveProperty('extractions');
        expect(data).toHaveProperty('count');
        expect(data).toHaveProperty('bestExtraction');
        expect(Array.isArray(data.extractions)).toBe(true);
      }
    }
  });

  test('time normalization works correctly', async ({ request }) => {
    const normalizationTests = [
      {
        text: "Meet at 2 PM",
        expectedNormalized: "14:00"
      },
      {
        text: "Wake up at 6:30 AM",
        expectedNormalized: "06:30"
      },
      {
        text: "Lunch at 12 PM",
        expectedNormalized: "12:00"
      },
      {
        text: "Midnight meeting at 12 AM",
        expectedNormalized: "00:00"
      }
    ];

    for (const test of normalizationTests) {
      const response = await request.post(`${API_BASE}/extract-time`, {
        data: { text: test.text }
      });

      expect(response.status()).toBe(200);
      
      const data = await response.json();
      const specificExtraction = data.extractions.find((e: any) => 
        e.timeType === 'specific' && e.normalizedTime === test.expectedNormalized
      );
      
      expect(specificExtraction).toBeTruthy();
    }
  });

  test('integration with refinement endpoint', async ({ request }) => {
    const transcript = "I want to exercise at 7 AM every morning";
    
    const response = await request.post(`${API_BASE}/refine`, {
      data: {
        request: {
          transcript,
          language: "en"
        }
      }
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.extractedTime).toBeTruthy();
    expect(data.needsTimeCllarification).toBe(false);
    
    // The extracted time should be meaningful
    expect(data.extractedTime).toMatch(/morning|07:00|daily/);
  });

  test('time clarification logic works correctly', async ({ request }) => {
    const testCases = [
      {
        transcript: "I want to exercise at 7 AM every day",
        shouldNeedClarification: false
      },
      {
        transcript: "I want to exercise more",
        shouldNeedClarification: true
      },
      {
        transcript: "I want to exercise sometime",
        shouldNeedClarification: true
      },
      {
        transcript: "I want to read every morning",
        shouldNeedClarification: false
      }
    ];

    for (const testCase of testCases) {
      const response = await request.post(`${API_BASE}/refine`, {
        data: {
          request: {
            transcript: testCase.transcript,
            language: "en"
          }
        }
      });

      expect(response.status()).toBe(200);
      
      const data = await response.json();
      expect(data.needsTimeCllarification).toBe(testCase.shouldNeedClarification);
      
      if (testCase.shouldNeedClarification) {
        expect(data.followUpQuestion).toBeTruthy();
        expect(data.followUpQuestion).toContain('time');
      }
    }
  });
});
