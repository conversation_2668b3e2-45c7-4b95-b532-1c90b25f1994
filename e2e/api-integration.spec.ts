import { test, expect } from '@playwright/test';

test.describe('Backend API Integration Tests', () => {
  const API_BASE = 'http://localhost:3001/api/refinement';

  test.beforeAll(async () => {
    // Ensure backend is running
    const response = await fetch(`${API_BASE}/health`);
    expect(response.status).toBe(200);
  });

  test('health endpoint returns correct status', async ({ request }) => {
    const response = await request.get(`${API_BASE}/health`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('status');
    expect(data).toHaveProperty('timestamp');
    expect(data).toHaveProperty('services');
    expect(data).toHaveProperty('version');
    expect(data.services).toHaveProperty('openai');
    expect(data.services).toHaveProperty('timeExtraction');
    expect(data.services.timeExtraction).toBe(true);
  });

  test('info endpoint returns service information', async ({ request }) => {
    const response = await request.get(`${API_BASE}/info`);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('service');
    expect(data).toHaveProperty('version');
    expect(data).toHaveProperty('description');
    expect(data).toHaveProperty('endpoints');
    expect(data).toHaveProperty('features');
    expect(data.service).toContain('Coach Me');
  });

  test('refine endpoint with valid input', async ({ request }) => {
    const requestBody = {
      request: {
        transcript: "I want to exercise more and go to the gym every morning",
        language: "en"
      }
    };

    const response = await request.post(`${API_BASE}/refine`, {
      data: requestBody,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('refinedPrompt');
    expect(data).toHaveProperty('extractedTime');
    expect(data).toHaveProperty('needsTimeCllarification');
    expect(data).toHaveProperty('confidence');
    expect(data).toHaveProperty('originalTranscript');
    expect(data).toHaveProperty('processingTime');
    expect(data).toHaveProperty('timestamp');
    
    expect(data.originalTranscript).toBe(requestBody.request.transcript);
    expect(data.refinedPrompt).toBeTruthy();
    expect(typeof data.confidence).toBe('number');
    expect(data.confidence).toBeGreaterThan(0);
    expect(data.confidence).toBeLessThanOrEqual(1);
  });

  test('refine endpoint with time expressions', async ({ request }) => {
    const requestBody = {
      request: {
        transcript: "I will read for 30 minutes at 7 AM every day",
        language: "en"
      }
    };

    const response = await request.post(`${API_BASE}/refine`, {
      data: requestBody
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.extractedTime).toBeTruthy();
    expect(data.needsTimeCllarification).toBe(false);
  });

  test('refine endpoint with vague time expressions', async ({ request }) => {
    const requestBody = {
      request: {
        transcript: "I want to be more productive sometime",
        language: "en"
      }
    };

    const response = await request.post(`${API_BASE}/refine`, {
      data: requestBody
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.needsTimeCllarification).toBe(true);
    expect(data.followUpQuestion).toBeTruthy();
  });

  test('refine endpoint with coaching context', async ({ request }) => {
    const requestBody = {
      request: {
        transcript: "I want to exercise more",
        language: "en"
      },
      context: {
        preferredStyle: "motivational",
        userGoals: ["fitness", "health"],
        previousPrompts: ["I will eat healthier"]
      }
    };

    const response = await request.post(`${API_BASE}/refine`, {
      data: requestBody
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.refinedPrompt).toBeTruthy();
    expect(data.confidence).toBeGreaterThan(0);
  });

  test('refine endpoint validation - empty transcript', async ({ request }) => {
    const requestBody = {
      request: {
        transcript: "",
        language: "en"
      }
    };

    const response = await request.post(`${API_BASE}/refine`, {
      data: requestBody
    });

    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data).toHaveProperty('message');
    expect(data.message).toContain('empty');
  });

  test('refine endpoint validation - invalid language', async ({ request }) => {
    const requestBody = {
      request: {
        transcript: "I want to exercise",
        language: "invalid"
      }
    };

    const response = await request.post(`${API_BASE}/refine`, {
      data: requestBody
    });

    expect(response.status()).toBe(400);
  });

  test('refine endpoint validation - transcript too long', async ({ request }) => {
    const longTranscript = 'a'.repeat(1001); // Exceeds 1000 character limit
    
    const requestBody = {
      request: {
        transcript: longTranscript,
        language: "en"
      }
    };

    const response = await request.post(`${API_BASE}/refine`, {
      data: requestBody
    });

    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data.message).toContain('too long');
  });

  test('extract-time endpoint with time expressions', async ({ request }) => {
    const requestBody = {
      text: "I want to exercise at 7:30 AM every day and read at night"
    };

    const response = await request.post(`${API_BASE}/extract-time`, {
      data: requestBody
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('extractions');
    expect(data).toHaveProperty('count');
    expect(data).toHaveProperty('bestExtraction');
    expect(Array.isArray(data.extractions)).toBe(true);
    expect(data.count).toBeGreaterThan(0);
    expect(data.bestExtraction).toBeTruthy();
    
    // Should extract specific time and recurring pattern
    const hasSpecificTime = data.extractions.some((e: any) => 
      e.timeExpression.includes('7:30 AM') && e.timeType === 'specific'
    );
    const hasRecurringPattern = data.extractions.some((e: any) => 
      e.timeExpression.includes('every day') && e.timeType === 'recurring'
    );
    
    expect(hasSpecificTime).toBe(true);
    expect(hasRecurringPattern).toBe(true);
  });

  test('extract-time endpoint with no time expressions', async ({ request }) => {
    const requestBody = {
      text: "I want to be more productive and focused"
    };

    const response = await request.post(`${API_BASE}/extract-time`, {
      data: requestBody
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.count).toBe(0);
    expect(data.extractions).toHaveLength(0);
    expect(data.bestExtraction).toBeNull();
  });

  test('extract-time endpoint validation - missing text', async ({ request }) => {
    const response = await request.post(`${API_BASE}/extract-time`, {
      data: {}
    });

    expect(response.status()).toBe(400);
  });

  test('extract-time endpoint validation - invalid text type', async ({ request }) => {
    const requestBody = {
      text: 123 // Should be string
    };

    const response = await request.post(`${API_BASE}/extract-time`, {
      data: requestBody
    });

    expect(response.status()).toBe(400);
  });

  test('CORS headers are present', async ({ request }) => {
    const response = await request.get(`${API_BASE}/health`);
    
    // Check for CORS headers (these might not be present in all environments)
    const headers = response.headers();
    // CORS headers are typically added by the server, test what we can
    expect(response.status()).toBe(200);
  });

  test('API handles concurrent requests', async ({ request }) => {
    const requestBody = {
      request: {
        transcript: "I want to exercise more",
        language: "en"
      }
    };

    // Send multiple concurrent requests
    const promises = Array(5).fill(null).map(() => 
      request.post(`${API_BASE}/refine`, { data: requestBody })
    );

    const responses = await Promise.all(promises);
    
    // All should succeed
    responses.forEach(response => {
      expect(response.status()).toBe(200);
    });

    // All should return valid data
    const dataPromises = responses.map(r => r.json());
    const results = await Promise.all(dataPromises);
    
    results.forEach(data => {
      expect(data).toHaveProperty('refinedPrompt');
      expect(data).toHaveProperty('confidence');
    });
  });

  test('API response times are reasonable', async ({ request }) => {
    const requestBody = {
      request: {
        transcript: "I want to exercise more",
        language: "en"
      }
    };

    const startTime = Date.now();
    const response = await request.post(`${API_BASE}/refine`, {
      data: requestBody
    });
    const endTime = Date.now();

    expect(response.status()).toBe(200);
    
    const responseTime = endTime - startTime;
    expect(responseTime).toBeLessThan(10000); // Should respond within 10 seconds
    
    const data = await response.json();
    if (data.processingTime) {
      expect(data.processingTime).toBeLessThan(10000);
    }
  });

  test('API handles Hebrew language input', async ({ request }) => {
    const requestBody = {
      request: {
        transcript: "אני רוצה להתאמן יותר",
        language: "he"
      }
    };

    const response = await request.post(`${API_BASE}/refine`, {
      data: requestBody
    });

    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.refinedPrompt).toBeTruthy();
    expect(data.originalTranscript).toBe(requestBody.request.transcript);
  });
});
