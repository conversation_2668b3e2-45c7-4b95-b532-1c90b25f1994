{"extends": "./tsconfig.json", "compilerOptions": {"module": "commonjs", "target": "es2020", "lib": ["es2020"], "outDir": "./dist", "rootDir": "./src", "noEmit": false, "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "sourceMap": true, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/backend/**/*", "src/types/**/*", "src/services/**/*", "src/utils/**/*"], "exclude": ["node_modules", "dist", "src/app/**/*", "src/components/**/*", "src/hooks/**/*", "src/store/**/*", "**/*.test.ts", "**/*.spec.ts"]}