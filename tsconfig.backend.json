{"extends": "./tsconfig.json", "compilerOptions": {"module": "commonjs", "target": "es2020", "outDir": "./dist", "rootDir": "./src", "noEmit": false, "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false}, "include": ["src/backend/**/*", "src/types/**/*", "src/services/**/*", "src/utils/**/*"], "exclude": ["node_modules", "dist", "src/app/**/*", "src/components/**/*", "src/hooks/**/*", "src/store/**/*", "**/*.test.ts", "**/*.spec.ts"]}