import { useState, useCallback } from 'react';
import { refinementApi, ApiRefinementResponse } from '@/services/refinementApi';
import { CoachingContext } from '@/types/refinement';

interface UsePromptRefinementState {
  isRefining: boolean;
  refinedPrompt: string | null;
  extractedTime: string | null;
  needsTimeCllarification: boolean;
  followUpQuestion: string | null;
  confidence: number | null;
  error: string | null;
  processingTime: number | null;
}

interface UsePromptRefinementReturn extends UsePromptRefinementState {
  refinePrompt: (
    transcript: string,
    language?: 'en' | 'he',
    context?: CoachingContext
  ) => Promise<ApiRefinementResponse | null>;
  clearRefinement: () => void;
  clearError: () => void;
}

export const usePromptRefinement = (): UsePromptRefinementReturn => {
  const [state, setState] = useState<UsePromptRefinementState>({
    isRefining: false,
    refinedPrompt: null,
    extractedTime: null,
    needsTimeCllarification: false,
    followUpQuestion: null,
    confidence: null,
    error: null,
    processingTime: null,
  });

  const refinePrompt = useCallback(async (
    transcript: string,
    language: 'en' | 'he' = 'en',
    context?: CoachingContext
  ): Promise<ApiRefinementResponse | null> => {
    if (!transcript.trim()) {
      setState(prev => ({
        ...prev,
        error: 'Transcript cannot be empty',
      }));
      return null;
    }

    setState(prev => ({
      ...prev,
      isRefining: true,
      error: null,
    }));

    try {
      const response = await refinementApi.refinePrompt(transcript, language, context);

      setState(prev => ({
        ...prev,
        isRefining: false,
        refinedPrompt: response.refinedPrompt,
        extractedTime: response.extractedTime,
        needsTimeCllarification: response.needsTimeCllarification,
        followUpQuestion: response.followUpQuestion || null,
        confidence: response.confidence,
        processingTime: response.processingTime || null,
        error: null,
      }));

      return response;

    } catch (error) {
      const errorMessage = error instanceof Error 
        ? error.message 
        : 'Failed to refine prompt';

      setState(prev => ({
        ...prev,
        isRefining: false,
        error: errorMessage,
      }));

      console.error('Prompt refinement failed:', error);
      return null;
    }
  }, []);

  const clearRefinement = useCallback(() => {
    setState({
      isRefining: false,
      refinedPrompt: null,
      extractedTime: null,
      needsTimeCllarification: false,
      followUpQuestion: null,
      confidence: null,
      error: null,
      processingTime: null,
    });
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null,
    }));
  }, []);

  return {
    ...state,
    refinePrompt,
    clearRefinement,
    clearError,
  };
};
