import { useEffect, useRef, useCallback } from 'react';
import { useRecordingStore } from '@/store/recordingStore';
import { 
  SpeechRecognition, 
  SpeechRecognitionEvent, 
  SpeechRecognitionErrorEvent,
  AudioRecording,
  VoiceRecognitionConfig 
} from '@/types/audio';

const defaultConfig: VoiceRecognitionConfig = {
  language: 'en-US',
  continuous: true,
  interimResults: true,
  maxAlternatives: 1,
};

export const useVoiceRecording = (config: Partial<VoiceRecognitionConfig> = {}) => {
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  
  const {
    isRecording,
    transcript,
    error,
    isProcessing,
    hasPermission,
    status,
    setRecording,
    setTranscript,
    setError,
    setProcessing,
    setPermission,
    addRecording,
    clearError,
  } = useRecordingStore();

  const finalConfig = { ...defaultConfig, ...config };

  // Check for browser support
  const isSpeechRecognitionSupported = useCallback(() => {
    return 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window;
  }, []);

  // Request microphone permission
  const requestPermission = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setPermission(true);
      stream.getTracks().forEach(track => track.stop()); // Stop the stream immediately
      return true;
    } catch (err) {
      console.error('Microphone permission denied:', err);
      setPermission(false);
      setError('Microphone access denied. Please allow microphone access to use voice recording.');
      return false;
    }
  }, [setPermission, setError]);

  // Initialize speech recognition
  const initializeSpeechRecognition = useCallback(() => {
    if (!isSpeechRecognitionSupported()) {
      setError('Speech recognition is not supported in this browser.');
      return null;
    }

    const SpeechRecognitionClass = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognitionClass();
    
    recognition.continuous = finalConfig.continuous;
    recognition.interimResults = finalConfig.interimResults;
    recognition.lang = finalConfig.language;
    recognition.maxAlternatives = finalConfig.maxAlternatives;

    recognition.onstart = () => {
      console.log('Speech recognition started');
      clearError();
    };

    recognition.onresult = (event: SpeechRecognitionEvent) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        if (result.isFinal) {
          finalTranscript += result[0].transcript;
        } else {
          interimTranscript += result[0].transcript;
        }
      }

      setTranscript(finalTranscript + interimTranscript);
    };

    recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
      console.error('Speech recognition error:', event.error);
      let errorMessage = 'Speech recognition error occurred.';
      
      switch (event.error) {
        case 'no-speech':
          errorMessage = 'No speech detected. Please try again.';
          break;
        case 'audio-capture':
          errorMessage = 'Audio capture failed. Please check your microphone.';
          break;
        case 'not-allowed':
          errorMessage = 'Microphone access denied. Please allow microphone access.';
          break;
        case 'network':
          errorMessage = 'Network error occurred. Please check your connection.';
          break;
        default:
          errorMessage = `Speech recognition error: ${event.error}`;
      }
      
      setError(errorMessage);
      setRecording(false);
    };

    recognition.onend = () => {
      console.log('Speech recognition ended');
      if (isRecording) {
        // Restart recognition if we're still supposed to be recording
        recognition.start();
      }
    };

    return recognition;
  }, [finalConfig, isSpeechRecognitionSupported, setError, clearError, setTranscript, setRecording, isRecording]);

  // Start recording
  const startRecording = useCallback(async () => {
    if (isRecording) return;

    // Check permission first
    if (hasPermission === null) {
      const granted = await requestPermission();
      if (!granted) return;
    } else if (hasPermission === false) {
      setError('Microphone access is required for voice recording.');
      return;
    }

    try {
      setProcessing(true);
      clearError();

      // Initialize speech recognition
      const recognition = initializeSpeechRecognition();
      if (!recognition) {
        setProcessing(false);
        return;
      }

      recognitionRef.current = recognition;

      // Get audio stream for recording
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;

      // Set up MediaRecorder for audio blob
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const recording: AudioRecording = {
          id: Date.now().toString(),
          transcript: transcript,
          audioBlob,
          timestamp: new Date(),
        };
        addRecording(recording);
      };

      // Start recording
      mediaRecorder.start();
      recognition.start();
      setRecording(true);
      setProcessing(false);
      setTranscript('');

    } catch (err) {
      console.error('Failed to start recording:', err);
      setError('Failed to start recording. Please try again.');
      setProcessing(false);
    }
  }, [
    isRecording,
    hasPermission,
    transcript,
    requestPermission,
    initializeSpeechRecognition,
    setProcessing,
    clearError,
    setRecording,
    setTranscript,
    setError,
    addRecording,
  ]);

  // Stop recording
  const stopRecording = useCallback(() => {
    if (!isRecording) return;

    setProcessing(true);

    // Stop speech recognition
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      recognitionRef.current = null;
    }

    // Stop media recorder
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }

    // Stop audio stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    setRecording(false);
  }, [isRecording, setRecording, setProcessing]);

  // Toggle recording
  const toggleRecording = useCallback(() => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [isRecording, startRecording, stopRecording]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  return {
    // State
    isRecording,
    transcript,
    error,
    isProcessing,
    hasPermission,
    status,
    isSupported: isSpeechRecognitionSupported(),
    
    // Actions
    startRecording,
    stopRecording,
    toggleRecording,
    requestPermission,
    clearError,
  };
};
