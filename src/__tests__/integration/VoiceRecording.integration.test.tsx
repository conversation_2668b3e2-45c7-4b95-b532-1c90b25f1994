import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import HomePage from '@/app/page'

// Mock the Web Speech API
const mockSpeechRecognition = {
  continuous: false,
  interimResults: false,
  lang: 'en-US',
  maxAlternatives: 1,
  start: jest.fn(),
  stop: jest.fn(),
  abort: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  onstart: null,
  onend: null,
  onresult: null,
  onerror: null,
}

const mockMediaRecorder = {
  start: jest.fn(),
  stop: jest.fn(),
  ondataavailable: null,
  onstop: null,
  state: 'inactive',
}

// Mock getUserMedia
const mockGetUserMedia = jest.fn(() => 
  Promise.resolve({
    getTracks: () => [{ stop: jest.fn() }],
  })
)

beforeAll(() => {
  global.SpeechRecognition = jest.fn(() => mockSpeechRecognition)
  global.webkitSpeechRecognition = global.SpeechRecognition
  global.MediaRecorder = jest.fn(() => mockMediaRecorder)
  
  Object.defineProperty(navigator, 'mediaDevices', {
    writable: true,
    value: {
      getUserMedia: mockGetUserMedia,
    },
  })
})

describe('Voice Recording Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockSpeechRecognition.start.mockClear()
    mockSpeechRecognition.stop.mockClear()
    mockMediaRecorder.start.mockClear()
    mockMediaRecorder.stop.mockClear()
    mockGetUserMedia.mockClear()
  })

  it('renders the complete voice recording interface', () => {
    render(<HomePage />)
    
    expect(screen.getByText('Coach Me')).toBeInTheDocument()
    expect(screen.getByText('Tap the button below to start voice recording')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Start recording' })).toBeInTheDocument()
    expect(screen.getByText('Transcript')).toBeInTheDocument()
    expect(screen.getByText('Your speech will appear here...')).toBeInTheDocument()
  })

  it('shows microphone permission request when needed', async () => {
    // Mock permission denied
    mockGetUserMedia.mockRejectedValueOnce(new Error('Permission denied'))
    
    render(<HomePage />)
    
    const recordingButton = screen.getByRole('button', { name: 'Start recording' })
    fireEvent.click(recordingButton)
    
    await waitFor(() => {
      expect(screen.getByText('Microphone Access Required')).toBeInTheDocument()
      expect(screen.getByText('Please allow microphone access to use voice recording features.')).toBeInTheDocument()
    })
  })

  it('handles successful microphone permission', async () => {
    render(<HomePage />)
    
    const recordingButton = screen.getByRole('button', { name: 'Start recording' })
    fireEvent.click(recordingButton)
    
    await waitFor(() => {
      expect(mockGetUserMedia).toHaveBeenCalledWith({ audio: true })
    })
  })

  it('starts recording when button is clicked', async () => {
    render(<HomePage />)
    
    const recordingButton = screen.getByRole('button', { name: 'Start recording' })
    fireEvent.click(recordingButton)
    
    await waitFor(() => {
      expect(mockSpeechRecognition.start).toHaveBeenCalled()
      expect(mockMediaRecorder.start).toHaveBeenCalled()
    })
  })

  it('displays error messages correctly', async () => {
    // Mock speech recognition error
    const mockError = new Error('Speech recognition failed')
    mockSpeechRecognition.start.mockImplementationOnce(() => {
      throw mockError
    })
    
    render(<HomePage />)
    
    const recordingButton = screen.getByRole('button', { name: 'Start recording' })
    fireEvent.click(recordingButton)
    
    await waitFor(() => {
      expect(screen.getByText('Recording Error')).toBeInTheDocument()
    })
  })

  it('shows browser not supported message when speech recognition is unavailable', () => {
    // Temporarily remove speech recognition support
    const originalSpeechRecognition = global.SpeechRecognition
    const originalWebkitSpeechRecognition = global.webkitSpeechRecognition
    
    // @ts-ignore
    delete global.SpeechRecognition
    // @ts-ignore
    delete global.webkitSpeechRecognition
    
    render(<HomePage />)
    
    expect(screen.getByText('Browser Not Supported')).toBeInTheDocument()
    expect(screen.getByText(/Voice recording requires a modern browser/)).toBeInTheDocument()
    
    // Restore speech recognition
    global.SpeechRecognition = originalSpeechRecognition
    global.webkitSpeechRecognition = originalWebkitSpeechRecognition
  })

  it('updates transcript display during recording', async () => {
    render(<HomePage />)
    
    const recordingButton = screen.getByRole('button', { name: 'Start recording' })
    fireEvent.click(recordingButton)
    
    // Simulate speech recognition result
    await waitFor(() => {
      expect(screen.getByText('Listening...')).toBeInTheDocument()
    })
  })

  it('handles recording stop correctly', async () => {
    render(<HomePage />)
    
    const recordingButton = screen.getByRole('button', { name: 'Start recording' })
    
    // Start recording
    fireEvent.click(recordingButton)
    
    await waitFor(() => {
      expect(mockSpeechRecognition.start).toHaveBeenCalled()
    })
    
    // Stop recording
    fireEvent.click(recordingButton)
    
    await waitFor(() => {
      expect(mockSpeechRecognition.stop).toHaveBeenCalled()
      expect(mockMediaRecorder.stop).toHaveBeenCalled()
    })
  })

  it('shows instructions and compatibility information', () => {
    render(<HomePage />)
    
    expect(screen.getByText('How to use:')).toBeInTheDocument()
    expect(screen.getByText('• Tap the microphone button to start recording')).toBeInTheDocument()
    expect(screen.getByText('• Speak clearly into your device\'s microphone')).toBeInTheDocument()
    expect(screen.getByText('• Your speech will be transcribed in real-time')).toBeInTheDocument()
    expect(screen.getByText('• Tap the button again to stop recording')).toBeInTheDocument()
    expect(screen.getByText('Works best in Chrome, Edge, and Safari browsers')).toBeInTheDocument()
  })
})
