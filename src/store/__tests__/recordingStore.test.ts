import { renderHook, act } from '@testing-library/react'
import { useRecordingStore } from '../recordingStore'
import { AudioRecording } from '@/types/audio'

describe('recordingStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    const { result } = renderHook(() => useRecordingStore())
    act(() => {
      result.current.reset()
      result.current.clearRecordings()
    })
  })

  it('has correct initial state', () => {
    const { result } = renderHook(() => useRecordingStore())
    
    expect(result.current.isRecording).toBe(false)
    expect(result.current.transcript).toBe('')
    expect(result.current.error).toBe(null)
    expect(result.current.isProcessing).toBe(false)
    expect(result.current.hasPermission).toBe(null)
    expect(result.current.status).toBe('idle')
    expect(result.current.recordings).toEqual([])
    expect(result.current.currentRecording).toBe(null)
  })

  it('sets recording state correctly', () => {
    const { result } = renderHook(() => useRecordingStore())
    
    act(() => {
      result.current.setRecording(true)
    })
    
    expect(result.current.isRecording).toBe(true)
    expect(result.current.status).toBe('recording')
    expect(result.current.error).toBe(null)
  })

  it('sets transcript correctly', () => {
    const { result } = renderHook(() => useRecordingStore())
    const testTranscript = 'Hello world'
    
    act(() => {
      result.current.setTranscript(testTranscript)
    })
    
    expect(result.current.transcript).toBe(testTranscript)
  })

  it('sets error correctly', () => {
    const { result } = renderHook(() => useRecordingStore())
    const errorMessage = 'Microphone access denied'
    
    act(() => {
      result.current.setError(errorMessage)
    })
    
    expect(result.current.error).toBe(errorMessage)
    expect(result.current.status).toBe('error')
    expect(result.current.isRecording).toBe(false)
    expect(result.current.isProcessing).toBe(false)
  })

  it('sets processing state correctly', () => {
    const { result } = renderHook(() => useRecordingStore())
    
    act(() => {
      result.current.setProcessing(true)
    })
    
    expect(result.current.isProcessing).toBe(true)
    expect(result.current.status).toBe('processing')
  })

  it('sets permission correctly', () => {
    const { result } = renderHook(() => useRecordingStore())
    
    act(() => {
      result.current.setPermission(true)
    })
    
    expect(result.current.hasPermission).toBe(true)
  })

  it('adds recording correctly', () => {
    const { result } = renderHook(() => useRecordingStore())
    const mockRecording: AudioRecording = {
      id: '1',
      transcript: 'Test recording',
      audioBlob: new Blob(['test'], { type: 'audio/wav' }),
      timestamp: new Date(),
    }
    
    act(() => {
      result.current.addRecording(mockRecording)
    })
    
    expect(result.current.recordings).toHaveLength(1)
    expect(result.current.recordings[0]).toEqual(mockRecording)
    expect(result.current.currentRecording).toEqual(mockRecording)
    expect(result.current.transcript).toBe(mockRecording.transcript)
    expect(result.current.isProcessing).toBe(false)
    expect(result.current.status).toBe('idle')
  })

  it('adds multiple recordings in correct order', () => {
    const { result } = renderHook(() => useRecordingStore())
    const recording1: AudioRecording = {
      id: '1',
      transcript: 'First recording',
      audioBlob: new Blob(['test1'], { type: 'audio/wav' }),
      timestamp: new Date(),
    }
    const recording2: AudioRecording = {
      id: '2',
      transcript: 'Second recording',
      audioBlob: new Blob(['test2'], { type: 'audio/wav' }),
      timestamp: new Date(),
    }
    
    act(() => {
      result.current.addRecording(recording1)
      result.current.addRecording(recording2)
    })
    
    expect(result.current.recordings).toHaveLength(2)
    expect(result.current.recordings[0]).toEqual(recording2) // Most recent first
    expect(result.current.recordings[1]).toEqual(recording1)
    expect(result.current.currentRecording).toEqual(recording2)
  })

  it('clears recordings correctly', () => {
    const { result } = renderHook(() => useRecordingStore())
    const mockRecording: AudioRecording = {
      id: '1',
      transcript: 'Test recording',
      audioBlob: new Blob(['test'], { type: 'audio/wav' }),
      timestamp: new Date(),
    }
    
    act(() => {
      result.current.addRecording(mockRecording)
      result.current.clearRecordings()
    })
    
    expect(result.current.recordings).toEqual([])
    expect(result.current.currentRecording).toBe(null)
  })

  it('clears error correctly', () => {
    const { result } = renderHook(() => useRecordingStore())
    
    act(() => {
      result.current.setError('Test error')
      result.current.clearError()
    })
    
    expect(result.current.error).toBe(null)
    expect(result.current.status).toBe('idle')
  })

  it('resets state correctly', () => {
    const { result } = renderHook(() => useRecordingStore())
    const mockRecording: AudioRecording = {
      id: '1',
      transcript: 'Test recording',
      audioBlob: new Blob(['test'], { type: 'audio/wav' }),
      timestamp: new Date(),
    }
    
    act(() => {
      result.current.setRecording(true)
      result.current.setTranscript('Test transcript')
      result.current.setError('Test error')
      result.current.setProcessing(true)
      result.current.setPermission(true)
      result.current.addRecording(mockRecording)
      result.current.reset()
    })
    
    expect(result.current.isRecording).toBe(false)
    expect(result.current.transcript).toBe('')
    expect(result.current.error).toBe(null)
    expect(result.current.isProcessing).toBe(false)
    expect(result.current.hasPermission).toBe(null)
    expect(result.current.status).toBe('idle')
    // Recordings should be preserved during reset
    expect(result.current.recordings).toHaveLength(1)
  })
})
