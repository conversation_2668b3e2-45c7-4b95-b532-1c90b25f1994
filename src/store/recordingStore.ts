import { create } from 'zustand';
import { AudioRecording, RecordingState, RecordingStatus } from '@/types/audio';

interface RecordingStore extends RecordingState {
  // State
  recordings: AudioRecording[];
  currentRecording: AudioRecording | null;
  status: RecordingStatus;
  
  // Actions
  setRecording: (isRecording: boolean) => void;
  setTranscript: (transcript: string) => void;
  setError: (error: string | null) => void;
  setProcessing: (isProcessing: boolean) => void;
  setPermission: (hasPermission: boolean) => void;
  setStatus: (status: RecordingStatus) => void;
  
  addRecording: (recording: AudioRecording) => void;
  clearRecordings: () => void;
  clearError: () => void;
  reset: () => void;
}

const initialState: RecordingState = {
  isRecording: false,
  transcript: '',
  error: null,
  isProcessing: false,
  hasPermission: null,
};

export const useRecordingStore = create<RecordingStore>((set, get) => ({
  // Initial state
  ...initialState,
  recordings: [],
  currentRecording: null,
  status: 'idle',
  
  // Actions
  setRecording: (isRecording: boolean) => 
    set({ 
      isRecording,
      status: isRecording ? 'recording' : 'idle',
      error: null 
    }),
    
  setTranscript: (transcript: string) => 
    set({ transcript }),
    
  setError: (error: string | null) => 
    set({ 
      error,
      status: error ? 'error' : 'idle',
      isRecording: false,
      isProcessing: false 
    }),
    
  setProcessing: (isProcessing: boolean) => 
    set({ 
      isProcessing,
      status: isProcessing ? 'processing' : 'idle' 
    }),
    
  setPermission: (hasPermission: boolean) => 
    set({ hasPermission }),
    
  setStatus: (status: RecordingStatus) => 
    set({ status }),
    
  addRecording: (recording: AudioRecording) => 
    set((state) => ({
      recordings: [recording, ...state.recordings],
      currentRecording: recording,
      transcript: recording.transcript,
      isProcessing: false,
      status: 'idle'
    })),
    
  clearRecordings: () => 
    set({ recordings: [], currentRecording: null }),
    
  clearError: () => 
    set({ error: null, status: 'idle' }),
    
  reset: () => 
    set({
      ...initialState,
      recordings: get().recordings,
      status: 'idle'
    }),
}));
