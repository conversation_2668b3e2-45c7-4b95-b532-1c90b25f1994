import { RefinementRequest, RefinementResponse, CoachingContext } from '@/types/refinement';

export interface ApiRefinementRequest {
  request: {
    transcript: string;
    language: 'en' | 'he';
    context?: string;
  };
  context?: {
    previousPrompts?: string[];
    userGoals?: string[];
    preferredStyle?: 'motivational' | 'analytical' | 'supportive' | 'direct';
    timeZone?: string;
  };
}

export interface ApiRefinementResponse extends RefinementResponse {
  processingTime?: number;
  timestamp: string;
}

export interface TimeExtractionResponse {
  extractions: Array<{
    timeExpression: string;
    normalizedTime: string | null;
    timeType: string;
    confidence: number;
  }>;
  count: number;
  bestExtraction: any | null;
}

export class RefinementApiService {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:3001/api/refinement') {
    this.baseUrl = baseUrl;
  }

  /**
   * Refine a voice transcript using the backend API
   */
  async refinePrompt(
    transcript: string,
    language: 'en' | 'he' = 'en',
    context?: CoachingContext
  ): Promise<ApiRefinementResponse> {
    const requestBody: ApiRefinementRequest = {
      request: {
        transcript,
        language,
      },
      context,
    };

    try {
      const response = await fetch(`${this.baseUrl}/refine`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to refine prompt:', error);
      throw new Error(
        error instanceof Error 
          ? error.message 
          : 'Failed to connect to refinement service'
      );
    }
  }

  /**
   * Extract time expressions from text
   */
  async extractTimeExpressions(text: string): Promise<TimeExtractionResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/extract-time`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to extract time expressions:', error);
      throw new Error(
        error instanceof Error 
          ? error.message 
          : 'Failed to connect to time extraction service'
      );
    }
  }

  /**
   * Check the health of the refinement service
   */
  async healthCheck(): Promise<{
    status: 'ok' | 'error';
    timestamp: string;
    services: {
      openai: boolean;
      timeExtraction: boolean;
    };
    version: string;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        services: {
          openai: false,
          timeExtraction: false,
        },
        version: 'unknown',
      };
    }
  }

  /**
   * Get service information
   */
  async getServiceInfo(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/info`, {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get service info:', error);
      throw new Error('Failed to connect to refinement service');
    }
  }

  /**
   * Test the connection to the backend
   */
  async testConnection(): Promise<boolean> {
    try {
      const health = await this.healthCheck();
      return health.status === 'ok';
    } catch (error) {
      return false;
    }
  }
}

// Create a singleton instance
export const refinementApi = new RefinementApiService();
