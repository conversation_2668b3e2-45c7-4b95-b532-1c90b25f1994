import OpenAI from 'openai';
import { 
  RefinementRequest, 
  RefinementResponse, 
  PromptRefinementConfig,
  OpenAIError,
  CoachingContext 
} from '@/types/refinement';
import { TimeExtractionService } from '@/utils/timeExtraction';

export class OpenAIService {
  private openai: OpenAI;
  private config: PromptRefinementConfig;

  constructor(apiKey: string, config?: Partial<PromptRefinementConfig>) {
    this.openai = new OpenAI({
      apiKey: apiKey,
    });

    this.config = {
      maxTokens: 150,
      temperature: 0.7,
      model: 'gpt-4',
      systemPrompt: this.getSystemPrompt(),
      ...config,
    };
  }

  /**
   * Refine a raw transcript into a structured coaching prompt
   */
  async refinePrompt(
    request: RefinementRequest,
    context?: CoachingContext
  ): Promise<RefinementResponse> {
    try {
      // Extract time information first
      const timeExtractions = TimeExtractionService.extractTimeExpressions(request.transcript);
      const bestTimeExtraction = timeExtractions.length > 0 ? timeExtractions[0] : null;
      const needsTimeCllarification = TimeExtractionService.needsTimeCllarification(timeExtractions);

      // Create the refinement prompt
      const refinementPrompt = this.createRefinementPrompt(request, context, timeExtractions);

      // Call OpenAI API
      const completion = await this.openai.chat.completions.create({
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: this.config.systemPrompt,
          },
          {
            role: 'user',
            content: refinementPrompt,
          },
        ],
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
      });

      const refinedContent = completion.choices[0]?.message?.content;
      if (!refinedContent) {
        throw new OpenAIError('No response received from OpenAI');
      }

      // Parse the refined response
      const refinedPrompt = this.parseRefinedResponse(refinedContent);

      // Generate follow-up question if needed
      const followUpQuestion = needsTimeCllarification 
        ? TimeExtractionService.generateTimeCllarificationQuestion(timeExtractions)
        : undefined;

      return {
        refinedPrompt,
        extractedTime: bestTimeExtraction?.normalizedTime || null,
        needsTimeCllarification,
        followUpQuestion,
        confidence: this.calculateConfidence(refinedContent, timeExtractions),
        originalTranscript: request.transcript,
      };

    } catch (error) {
      if (error instanceof OpenAIError) {
        throw error;
      }
      
      console.error('OpenAI API error:', error);
      throw new OpenAIError(
        'Failed to refine prompt using OpenAI',
        error
      );
    }
  }

  /**
   * Create the system prompt for coaching context
   */
  private getSystemPrompt(): string {
    return `You are an AI coaching assistant that helps refine raw voice transcripts into clear, actionable coaching prompts.

Your task is to:
1. Transform informal speech into clear, motivational coaching language
2. Identify the core goal or intention
3. Make the language positive and action-oriented
4. Keep the user's original intent intact
5. Use encouraging, supportive tone

Guidelines:
- Convert "I want to" into "I will" or "I commit to"
- Make vague goals more specific when possible
- Use present tense and active voice
- Keep responses concise (1-2 sentences)
- Maintain the user's personal style while improving clarity

Examples:
Input: "I want to like exercise more and maybe go to the gym"
Output: "I will build a consistent exercise routine by going to the gym regularly and finding activities I enjoy."

Input: "I need to stop procrastinating on my work stuff"
Output: "I will tackle my work tasks promptly and create a productive daily routine."

Respond only with the refined prompt, no additional explanation.`;
  }

  /**
   * Create the refinement prompt for OpenAI
   */
  private createRefinementPrompt(
    request: RefinementRequest,
    context?: CoachingContext,
    timeExtractions: any[] = []
  ): string {
    let prompt = `Please refine this voice transcript into a clear coaching prompt:\n\n"${request.transcript}"`;

    // Add language context
    if (request.language === 'he') {
      prompt += '\n\nNote: This was originally spoken in Hebrew. Please maintain cultural context while refining.';
    }

    // Add coaching context if available
    if (context) {
      if (context.preferredStyle) {
        prompt += `\n\nCoaching style preference: ${context.preferredStyle}`;
      }
      
      if (context.userGoals && context.userGoals.length > 0) {
        prompt += `\n\nUser's previous goals: ${context.userGoals.join(', ')}`;
      }
    }

    // Add time context if extracted
    if (timeExtractions.length > 0) {
      const timeInfo = timeExtractions.map(t => t.timeExpression).join(', ');
      prompt += `\n\nTime references detected: ${timeInfo}`;
    }

    return prompt;
  }

  /**
   * Parse the refined response from OpenAI
   */
  private parseRefinedResponse(content: string): string {
    // Clean up the response
    let refined = content.trim();
    
    // Remove quotes if the AI wrapped the response in them
    if (refined.startsWith('"') && refined.endsWith('"')) {
      refined = refined.slice(1, -1);
    }
    
    // Ensure it ends with proper punctuation
    if (!/[.!?]$/.test(refined)) {
      refined += '.';
    }
    
    return refined;
  }

  /**
   * Calculate confidence score for the refinement
   */
  private calculateConfidence(refinedContent: string, timeExtractions: any[]): number {
    let confidence = 0.8; // Base confidence
    
    // Increase confidence if time was clearly extracted
    if (timeExtractions.length > 0 && timeExtractions[0].confidence > 0.8) {
      confidence += 0.1;
    }
    
    // Decrease confidence if the refined content is very short or generic
    if (refinedContent.length < 20) {
      confidence -= 0.2;
    }
    
    // Increase confidence if the refined content has action words
    const actionWords = ['will', 'commit', 'practice', 'develop', 'create', 'build', 'achieve'];
    const hasActionWords = actionWords.some(word => 
      refinedContent.toLowerCase().includes(word)
    );
    
    if (hasActionWords) {
      confidence += 0.1;
    }
    
    return Math.min(Math.max(confidence, 0), 1);
  }

  /**
   * Test the OpenAI connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: 'Test connection. Respond with "OK".',
          },
        ],
        max_tokens: 5,
      });

      return completion.choices[0]?.message?.content?.includes('OK') || false;
    } catch (error) {
      console.error('OpenAI connection test failed:', error);
      return false;
    }
  }

  /**
   * Get usage statistics
   */
  async getUsageStats(): Promise<any> {
    // This would typically connect to OpenAI's usage API
    // For now, return a placeholder
    return {
      tokensUsed: 0,
      requestsToday: 0,
      costToday: 0,
    };
  }
}
