import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  const app = await NestFactory.create(AppModule);
  
  // Enable CORS for frontend integration
  app.enableCors({
    origin: ['http://localhost:3000', 'http://localhost:3001'],
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    })
  );

  // Global prefix for API routes
  app.setGlobalPrefix('api');

  const port = process.env.BACKEND_PORT || 3001;
  
  await app.listen(port);
  
  logger.log(`🚀 Backend server running on http://localhost:${port}`);
  logger.log(`📚 API documentation available at http://localhost:${port}/api/refinement/info`);
}

bootstrap().catch((error) => {
  console.error('Failed to start backend server:', error);
  process.exit(1);
});
