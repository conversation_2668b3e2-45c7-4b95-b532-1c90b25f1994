import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OpenAIService } from '@/services/openaiService';
import { TimeExtractionService } from '@/utils/timeExtraction';
import { 
  RefinementRequest, 
  RefinementResponse, 
  CoachingContext,
  OpenAIError,
  ValidationError 
} from '@/types/refinement';
import { RefinementRequestDto, CoachingContextDto } from './dto/refinement.dto';

@Injectable()
export class RefinementService {
  private readonly logger = new Logger(RefinementService.name);
  private openaiService: OpenAIService;

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    
    if (!apiKey) {
      this.logger.warn('OpenAI API key not found. Service will operate in mock mode.');
      this.openaiService = null;
    } else {
      this.openaiService = new OpenAIService(apiKey, {
        model: this.configService.get<string>('OPENAI_MODEL', 'gpt-4'),
        maxTokens: parseInt(this.configService.get<string>('OPENAI_MAX_TOKENS', '150')),
        temperature: parseFloat(this.configService.get<string>('OPENAI_TEMPERATURE', '0.7')),
      });
    }
  }

  /**
   * Refine a voice transcript into a coaching prompt
   */
  async refinePrompt(
    requestDto: RefinementRequestDto,
    contextDto?: CoachingContextDto
  ): Promise<RefinementResponse> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Refining prompt for transcript: "${requestDto.transcript.substring(0, 50)}..."`);

      // Validate input
      this.validateRequest(requestDto);

      // Convert DTOs to internal types
      const request: RefinementRequest = {
        transcript: requestDto.transcript,
        language: requestDto.language,
        context: requestDto.context,
      };

      const context: CoachingContext | undefined = contextDto ? {
        previousPrompts: contextDto.previousPrompts || [],
        userGoals: contextDto.userGoals || [],
        preferredStyle: contextDto.preferredStyle || 'supportive',
        timeZone: contextDto.timeZone,
      } : undefined;

      let response: RefinementResponse;

      if (this.openaiService) {
        // Use real OpenAI service
        response = await this.openaiService.refinePrompt(request, context);
      } else {
        // Use mock service for development/testing
        response = await this.mockRefinePrompt(request);
      }

      const processingTime = Date.now() - startTime;
      this.logger.log(`Prompt refined successfully in ${processingTime}ms`);

      return {
        ...response,
        processingTime,
        timestamp: new Date().toISOString(),
      } as any;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Failed to refine prompt after ${processingTime}ms:`, error);
      
      if (error instanceof OpenAIError || error instanceof ValidationError) {
        throw error;
      }
      
      throw new Error('Internal server error during prompt refinement');
    }
  }

  /**
   * Extract time expressions from text
   */
  extractTimeExpressions(text: string) {
    try {
      return TimeExtractionService.extractTimeExpressions(text);
    } catch (error) {
      this.logger.error('Failed to extract time expressions:', error);
      return [];
    }
  }

  /**
   * Health check for the service
   */
  async healthCheck() {
    const services = {
      openai: false,
      timeExtraction: true, // Always available
    };

    // Test OpenAI connection if available
    if (this.openaiService) {
      try {
        services.openai = await this.openaiService.testConnection();
      } catch (error) {
        this.logger.warn('OpenAI health check failed:', error);
      }
    }

    return {
      status: services.openai || !this.openaiService ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      services,
      version: '1.0.0',
    };
  }

  /**
   * Validate the refinement request
   */
  private validateRequest(request: RefinementRequestDto): void {
    if (!request.transcript || request.transcript.trim().length === 0) {
      throw new ValidationError('Transcript cannot be empty', 'transcript');
    }

    if (request.transcript.length > 1000) {
      throw new ValidationError('Transcript is too long (max 1000 characters)', 'transcript');
    }

    if (!['en', 'he'].includes(request.language)) {
      throw new ValidationError('Language must be either "en" or "he"', 'language');
    }
  }

  /**
   * Mock refinement service for development/testing
   */
  private async mockRefinePrompt(request: RefinementRequest): Promise<RefinementResponse> {
    this.logger.log('Using mock refinement service');

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Extract time information
    const timeExtractions = TimeExtractionService.extractTimeExpressions(request.transcript);
    const bestTimeExtraction = timeExtractions.length > 0 ? timeExtractions[0] : null;
    const needsTimeCllarification = TimeExtractionService.needsTimeCllarification(timeExtractions);

    // Generate a mock refined prompt
    const refinedPrompt = this.generateMockRefinedPrompt(request.transcript);

    // Generate follow-up question if needed
    const followUpQuestion = needsTimeCllarification 
      ? TimeExtractionService.generateTimeCllarificationQuestion(timeExtractions)
      : undefined;

    return {
      refinedPrompt,
      extractedTime: bestTimeExtraction?.normalizedTime || null,
      needsTimeCllarification,
      followUpQuestion,
      confidence: 0.85,
      originalTranscript: request.transcript,
    };
  }

  /**
   * Generate a mock refined prompt for testing
   */
  private generateMockRefinedPrompt(transcript: string): string {
    const text = transcript.toLowerCase();
    
    // Simple rule-based refinement for demo purposes
    let refined = transcript;
    
    // Convert "I want to" to "I will"
    refined = refined.replace(/I want to/gi, 'I will');
    refined = refined.replace(/I need to/gi, 'I will');
    refined = refined.replace(/I should/gi, 'I will');
    
    // Make it more action-oriented
    if (text.includes('exercise') || text.includes('workout') || text.includes('gym')) {
      refined = 'I will build a consistent exercise routine and stay committed to my fitness goals.';
    } else if (text.includes('read') || text.includes('book')) {
      refined = 'I will develop a regular reading habit and expand my knowledge.';
    } else if (text.includes('work') || text.includes('productive')) {
      refined = 'I will create a productive work routine and accomplish my professional goals.';
    } else if (text.includes('learn') || text.includes('study')) {
      refined = 'I will commit to continuous learning and skill development.';
    }
    
    // Ensure proper punctuation
    if (!/[.!?]$/.test(refined)) {
      refined += '.';
    }
    
    return refined;
  }
}
