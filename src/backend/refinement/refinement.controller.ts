import { 
  Controller, 
  Post, 
  Get, 
  Body, 
  HttpException, 
  HttpStatus,
  Logger,
  ValidationPipe,
  UsePipes
} from '@nestjs/common';
import { RefinementService } from './refinement.service';
import { 
  RefinementRequestDto, 
  CoachingContextDto, 
  RefinementResponseDto,
  HealthCheckResponseDto 
} from './dto/refinement.dto';
import { ValidationError, OpenAIError } from '@/types/refinement';

@Controller('api/refinement')
export class RefinementController {
  private readonly logger = new Logger(RefinementController.name);

  constructor(private readonly refinementService: RefinementService) {}

  /**
   * Refine a voice transcript into a coaching prompt
   */
  @Post('refine')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async refinePrompt(
    @Body() body: { request: RefinementRequestDto; context?: CoachingContextDto }
  ): Promise<RefinementResponseDto> {
    try {
      this.logger.log('Received refinement request');
      
      const result = await this.refinementService.refinePrompt(
        body.request,
        body.context
      );

      return result;

    } catch (error) {
      this.logger.error('Refinement request failed:', error);

      if (error instanceof ValidationError) {
        throw new HttpException(
          {
            message: error.message,
            field: error.field,
            code: error.code,
          },
          HttpStatus.BAD_REQUEST
        );
      }

      if (error instanceof OpenAIError) {
        throw new HttpException(
          {
            message: 'AI service temporarily unavailable',
            code: error.code,
          },
          HttpStatus.BAD_GATEWAY
        );
      }

      throw new HttpException(
        {
          message: 'Internal server error',
          code: 'INTERNAL_ERROR',
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Extract time expressions from text
   */
  @Post('extract-time')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async extractTime(@Body() body: { text: string }) {
    try {
      if (!body.text || typeof body.text !== 'string') {
        throw new HttpException(
          { message: 'Text is required and must be a string' },
          HttpStatus.BAD_REQUEST
        );
      }

      const extractions = this.refinementService.extractTimeExpressions(body.text);
      
      return {
        extractions,
        count: extractions.length,
        bestExtraction: extractions.length > 0 ? extractions[0] : null,
      };

    } catch (error) {
      this.logger.error('Time extraction failed:', error);
      
      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        { message: 'Failed to extract time expressions' },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Health check endpoint
   */
  @Get('health')
  async healthCheck(): Promise<HealthCheckResponseDto> {
    try {
      return await this.refinementService.healthCheck();
    } catch (error) {
      this.logger.error('Health check failed:', error);
      
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        services: {
          openai: false,
          timeExtraction: false,
        },
        version: '1.0.0',
      };
    }
  }

  /**
   * Get service information
   */
  @Get('info')
  async getInfo() {
    return {
      service: 'Coach Me - Prompt Refinement API',
      version: '1.0.0',
      description: 'AI-powered voice transcript refinement for coaching prompts',
      endpoints: {
        'POST /api/refinement/refine': 'Refine voice transcript into coaching prompt',
        'POST /api/refinement/extract-time': 'Extract time expressions from text',
        'GET /api/refinement/health': 'Service health check',
        'GET /api/refinement/info': 'Service information',
      },
      features: [
        'OpenAI GPT-4 integration',
        'Natural language time extraction',
        'Multi-language support (EN/HE)',
        'Coaching context awareness',
        'Request validation',
        'Error handling',
      ],
    };
  }
}
