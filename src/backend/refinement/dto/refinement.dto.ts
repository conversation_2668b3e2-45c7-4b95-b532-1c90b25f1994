import { IsString, <PERSON><PERSON><PERSON>al, <PERSON>In, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';

export class RefinementRequestDto {
  @IsString()
  @MinLength(1, { message: 'Transcript cannot be empty' })
  @MaxLength(1000, { message: 'Transcript is too long (max 1000 characters)' })
  @Transform(({ value }) => value?.trim())
  transcript: string;

  @IsString()
  @IsIn(['en', 'he'], { message: 'Language must be either "en" or "he"' })
  language: 'en' | 'he';

  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Context is too long (max 500 characters)' })
  context?: string;
}

export class CoachingContextDto {
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  previousPrompts?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  userGoals?: string[];

  @IsOptional()
  @IsString()
  @IsIn(['motivational', 'analytical', 'supportive', 'direct'])
  preferredStyle?: 'motivational' | 'analytical' | 'supportive' | 'direct';

  @IsOptional()
  @IsString()
  timeZone?: string;
}

export class RefinementResponseDto {
  refinedPrompt: string;
  extractedTime: string | null;
  needsTimeCllarification: boolean;
  followUpQuestion?: string;
  confidence: number;
  originalTranscript: string;
  processingTime?: number;
  timestamp: string;
}

export class HealthCheckResponseDto {
  status: 'ok' | 'error';
  timestamp: string;
  services: {
    openai: boolean;
    timeExtraction: boolean;
  };
  version: string;
}
