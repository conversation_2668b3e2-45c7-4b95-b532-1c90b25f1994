@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
  
  body {
    @apply text-gray-900;
  }
}

@layer components {
  .recording-button {
    @apply relative flex items-center justify-center w-24 h-24 rounded-full transition-all duration-300 ease-in-out;
  }
  
  .recording-button.idle {
    @apply bg-primary-500 hover:bg-primary-600 shadow-lg hover:shadow-xl;
  }
  
  .recording-button.recording {
    @apply bg-red-500 animate-pulse shadow-2xl;
  }
  
  .recording-button.processing {
    @apply bg-yellow-500 animate-spin;
  }
  
  .recording-pulse {
    @apply absolute inset-0 rounded-full bg-red-400 opacity-30 animate-ping;
  }
  
  .transcript-container {
    @apply bg-white rounded-lg shadow-md p-4 min-h-[120px] border border-gray-200;
  }
  
  .error-message {
    @apply bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500;
  }
}
