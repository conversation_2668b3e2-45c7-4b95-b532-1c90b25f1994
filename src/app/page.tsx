'use client';

import React, { useEffect } from 'react';
import RecordingButton from '@/components/RecordingButton';
import TranscriptDisplay from '@/components/TranscriptDisplay';
import ErrorDisplay from '@/components/ErrorDisplay';
import RefinedPromptDisplay from '@/components/RefinedPromptDisplay';
import { useVoiceRecording } from '@/hooks/useVoiceRecording';
import { usePromptRefinement } from '@/hooks/usePromptRefinement';
import { useRecordingStore } from '@/store/recordingStore';

export default function HomePage() {
  const {
    hasPermission,
    requestPermission,
    isSupported,
    error
  } = useVoiceRecording();

  const { transcript, isRecording } = useRecordingStore();
  const { refinePrompt, isRefining } = usePromptRefinement();

  useEffect(() => {
    // Check for browser support on mount
    if (!isSupported) {
      console.warn('Speech recognition is not supported in this browser');
    }
  }, [isSupported]);

  // Auto-refine transcript when recording stops and transcript is available
  useEffect(() => {
    if (!isRecording && transcript && transcript.trim().length > 0) {
      const timeoutId = setTimeout(() => {
        refinePrompt(transcript, 'en');
      }, 1000); // Wait 1 second after recording stops

      return () => clearTimeout(timeoutId);
    }
  }, [isRecording, transcript, refinePrompt]);

  const handleRequestPermission = async () => {
    await requestPermission();
  };

  if (!isSupported) {
    return (
      <div className="text-center py-12">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <svg className="w-12 h-12 text-yellow-500 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
          </svg>
          <h2 className="text-lg font-semibold text-yellow-800 mb-2">
            Browser Not Supported
          </h2>
          <p className="text-yellow-700 text-sm">
            Voice recording requires a modern browser with Web Speech API support. 
            Please try using Chrome, Edge, or Safari.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Coach Me
        </h1>
        <p className="text-gray-600 text-sm">
          Tap the button below to start voice recording
        </p>
      </div>

      {/* Permission Request */}
      {hasPermission === false && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <svg className="w-6 h-6 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2a3 3 0 0 1 3 3v6a3 3 0 0 1-6 0V5a3 3 0 0 1 3-3Z" />
              <path d="M19 10v1a7 7 0 0 1-14 0v-1a1 1 0 0 1 2 0v1a5 5 0 0 0 10 0v-1a1 1 0 1 1 2 0Z" />
            </svg>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-blue-800">
                Microphone Access Required
              </h3>
              <p className="text-sm text-blue-700 mt-1">
                Please allow microphone access to use voice recording features.
              </p>
            </div>
            <button
              onClick={handleRequestPermission}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Allow Access
            </button>
          </div>
        </div>
      )}

      {/* Error Display */}
      <ErrorDisplay />

      {/* Recording Interface */}
      <div className="text-center py-8">
        <RecordingButton className="mx-auto mb-6" />
        
        <div className="space-y-2 text-sm text-gray-600">
          <p>Tap to start recording</p>
          <p className="text-xs">Tap again to stop</p>
        </div>
      </div>

      {/* Transcript Display */}
      <TranscriptDisplay />

      {/* Refined Prompt Display */}
      <RefinedPromptDisplay />

      {/* Instructions */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-800 mb-2">
          How to use:
        </h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Tap the microphone button to start recording</li>
          <li>• Speak clearly into your device's microphone</li>
          <li>• Your speech will be transcribed in real-time</li>
          <li>• Tap the button again to stop recording</li>
        </ul>
      </div>

      {/* Browser Compatibility Note */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Works best in Chrome, Edge, and Safari browsers
        </p>
      </div>
    </div>
  );
}
