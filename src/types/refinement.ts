// Backend service interfaces for AI prompt refinement
export interface RefinementRequest {
  transcript: string;
  language: 'en' | 'he';
  context?: string;
}

export interface RefinementResponse {
  refinedPrompt: string;
  extractedTime: string | null;
  needsTimeCllarification: boolean;
  followUpQuestion?: string;
  confidence: number;
  originalTranscript: string;
  processingTime?: number;
  timestamp?: string;
}

export interface TimeExtraction {
  timeExpression: string;
  normalizedTime: string | null;
  timeType: 'specific' | 'recurring' | 'relative' | 'unclear';
  confidence: number;
}

export interface PromptRefinementConfig {
  maxTokens: number;
  temperature: number;
  model: string;
  systemPrompt: string;
}

export interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface CoachingContext {
  previousPrompts: string[];
  userGoals: string[];
  preferredStyle: 'motivational' | 'analytical' | 'supportive' | 'direct';
  timeZone?: string;
}

// Error types
export class RefinementError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = 'RefinementError';
  }
}

export class OpenAIError extends RefinementError {
  constructor(message: string, public originalError?: any) {
    super(message, 'OPENAI_ERROR', 502);
    this.name = 'OpenAIError';
  }
}

export class ValidationError extends RefinementError {
  constructor(message: string, public field?: string) {
    super(message, 'VALIDATION_ERROR', 400);
    this.name = 'ValidationError';
  }
}
