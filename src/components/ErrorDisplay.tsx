'use client';

import React from 'react';
import { useRecordingStore } from '@/store/recordingStore';

interface ErrorDisplayProps {
  className?: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ className = '' }) => {
  const { error, clearError } = useRecordingStore();

  if (!error) return null;

  return (
    <div className={`error-message ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-2">
          <svg 
            className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" 
            fill="currentColor" 
            viewBox="0 0 24 24"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
          </svg>
          <div>
            <p className="text-sm font-medium">Recording Error</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
        </div>
        
        <button
          onClick={clearError}
          className="ml-4 text-red-500 hover:text-red-700 transition-colors"
          aria-label="Dismiss error"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default ErrorDisplay;
