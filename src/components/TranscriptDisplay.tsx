'use client';

import React from 'react';
import { useRecordingStore } from '@/store/recordingStore';

interface TranscriptDisplayProps {
  className?: string;
  placeholder?: string;
}

const TranscriptDisplay: React.FC<TranscriptDisplayProps> = ({ 
  className = '',
  placeholder = 'Your speech will appear here...'
}) => {
  const { transcript, isRecording, isProcessing } = useRecordingStore();

  const getDisplayText = () => {
    if (isProcessing) {
      return 'Processing your speech...';
    }
    
    if (isRecording && !transcript) {
      return 'Listening...';
    }
    
    if (transcript) {
      return transcript;
    }
    
    return placeholder;
  };

  const getTextClass = () => {
    if (isProcessing) {
      return 'text-yellow-600 italic';
    }
    
    if (isRecording && !transcript) {
      return 'text-blue-600 italic animate-pulse';
    }
    
    if (transcript) {
      return 'text-gray-900';
    }
    
    return 'text-gray-500 italic';
  };

  return (
    <div className={`transcript-container ${className}`}>
      <div className="flex items-start justify-between mb-2">
        <h3 className="text-sm font-medium text-gray-700">Transcript</h3>
        {isRecording && (
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-red-600">Recording</span>
          </div>
        )}
      </div>
      
      <div className={`min-h-[80px] ${getTextClass()}`}>
        <p className="text-sm leading-relaxed whitespace-pre-wrap">
          {getDisplayText()}
        </p>
      </div>
      
      {transcript && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Words: {transcript.split(' ').filter(word => word.length > 0).length}</span>
            <span>Characters: {transcript.length}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default TranscriptDisplay;
