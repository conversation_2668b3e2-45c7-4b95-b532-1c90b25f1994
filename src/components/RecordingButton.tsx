'use client';

import React from 'react';
import { useVoiceRecording } from '@/hooks/useVoiceRecording';

interface RecordingButtonProps {
  className?: string;
}

const RecordingButton: React.FC<RecordingButtonProps> = ({ className = '' }) => {
  const { 
    isRecording, 
    isProcessing, 
    status, 
    toggleRecording,
    isSupported 
  } = useVoiceRecording();

  if (!isSupported) {
    return (
      <div className={`recording-button bg-gray-400 cursor-not-allowed ${className}`}>
        <span className="text-white text-xs">Not Supported</span>
      </div>
    );
  }

  const getButtonContent = () => {
    if (isProcessing) {
      return (
        <div className="loading-spinner"></div>
      );
    }

    if (isRecording) {
      return (
        <>
          <div className="recording-pulse"></div>
          <svg 
            className="w-8 h-8 text-white z-10" 
            fill="currentColor" 
            viewBox="0 0 24 24"
          >
            <rect x="6" y="6" width="12" height="12" rx="2" />
          </svg>
        </>
      );
    }

    return (
      <svg 
        className="w-8 h-8 text-white" 
        fill="currentColor" 
        viewBox="0 0 24 24"
      >
        <path d="M12 2a3 3 0 0 1 3 3v6a3 3 0 0 1-6 0V5a3 3 0 0 1 3-3Z" />
        <path d="M19 10v1a7 7 0 0 1-14 0v-1a1 1 0 0 1 2 0v1a5 5 0 0 0 10 0v-1a1 1 0 1 1 2 0Z" />
        <path d="M12 18.5a1 1 0 0 1 1 1v1a1 1 0 1 1-2 0v-1a1 1 0 0 1 1-1Z" />
      </svg>
    );
  };

  const getButtonClass = () => {
    const baseClass = `recording-button ${className}`;
    
    if (isProcessing) {
      return `${baseClass} processing`;
    }
    
    if (isRecording) {
      return `${baseClass} recording`;
    }
    
    return `${baseClass} idle`;
  };

  const getAriaLabel = () => {
    if (isProcessing) return 'Processing recording...';
    if (isRecording) return 'Stop recording';
    return 'Start recording';
  };

  return (
    <button
      onClick={toggleRecording}
      disabled={isProcessing}
      className={getButtonClass()}
      aria-label={getAriaLabel()}
      type="button"
    >
      {getButtonContent()}
    </button>
  );
};

export default RecordingButton;
