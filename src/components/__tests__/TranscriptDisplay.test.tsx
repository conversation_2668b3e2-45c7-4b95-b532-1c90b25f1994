import React from 'react'
import { render, screen } from '@testing-library/react'
import { useRecordingStore } from '@/store/recordingStore'
import TranscriptDisplay from '../TranscriptDisplay'

// Mock the store
jest.mock('@/store/recordingStore')
const mockUseRecordingStore = useRecordingStore as jest.MockedFunction<typeof useRecordingStore>

describe('TranscriptDisplay', () => {
  const defaultStoreState = {
    isRecording: false,
    transcript: '',
    error: null,
    isProcessing: false,
    hasPermission: null,
    recordings: [],
    currentRecording: null,
    status: 'idle' as const,
    setRecording: jest.fn(),
    setTranscript: jest.fn(),
    setError: jest.fn(),
    setProcessing: jest.fn(),
    setPermission: jest.fn(),
    setStatus: jest.fn(),
    addRecording: jest.fn(),
    clearRecordings: jest.fn(),
    clearError: jest.fn(),
    reset: jest.fn(),
  }

  beforeEach(() => {
    mockUseRecordingStore.mockReturnValue(defaultStoreState)
  })

  it('renders the transcript container', () => {
    render(<TranscriptDisplay />)
    expect(screen.getByText('Transcript')).toBeInTheDocument()
  })

  it('shows placeholder text when no transcript', () => {
    render(<TranscriptDisplay />)
    expect(screen.getByText('Your speech will appear here...')).toBeInTheDocument()
  })

  it('shows custom placeholder text', () => {
    render(<TranscriptDisplay placeholder="Custom placeholder" />)
    expect(screen.getByText('Custom placeholder')).toBeInTheDocument()
  })

  it('shows listening message when recording but no transcript', () => {
    mockUseRecordingStore.mockReturnValue({
      ...defaultStoreState,
      isRecording: true,
    })

    render(<TranscriptDisplay />)
    expect(screen.getByText('Listening...')).toBeInTheDocument()
    expect(screen.getByText('Recording')).toBeInTheDocument()
  })

  it('shows processing message when processing', () => {
    mockUseRecordingStore.mockReturnValue({
      ...defaultStoreState,
      isProcessing: true,
    })

    render(<TranscriptDisplay />)
    expect(screen.getByText('Processing your speech...')).toBeInTheDocument()
  })

  it('displays transcript text when available', () => {
    const testTranscript = 'Hello world this is a test'
    mockUseRecordingStore.mockReturnValue({
      ...defaultStoreState,
      transcript: testTranscript,
    })

    render(<TranscriptDisplay />)
    expect(screen.getByText(testTranscript)).toBeInTheDocument()
  })

  it('shows word and character count when transcript is available', () => {
    const testTranscript = 'Hello world test'
    mockUseRecordingStore.mockReturnValue({
      ...defaultStoreState,
      transcript: testTranscript,
    })

    render(<TranscriptDisplay />)
    expect(screen.getByText('Words: 3')).toBeInTheDocument()
    expect(screen.getByText('Characters: 16')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    const { container } = render(<TranscriptDisplay className="custom-class" />)
    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('shows recording indicator when recording', () => {
    mockUseRecordingStore.mockReturnValue({
      ...defaultStoreState,
      isRecording: true,
    })

    render(<TranscriptDisplay />)
    const recordingIndicator = screen.getByText('Recording')
    expect(recordingIndicator).toBeInTheDocument()
    expect(recordingIndicator).toHaveClass('text-red-600')
  })

  it('handles empty transcript correctly for word count', () => {
    mockUseRecordingStore.mockReturnValue({
      ...defaultStoreState,
      transcript: '   ',
    })

    render(<TranscriptDisplay />)
    expect(screen.getByText('Words: 0')).toBeInTheDocument()
    expect(screen.getByText('Characters: 3')).toBeInTheDocument()
  })
})
