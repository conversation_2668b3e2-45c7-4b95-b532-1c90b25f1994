import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { useRecordingStore } from '@/store/recordingStore'
import ErrorDisplay from '../ErrorDisplay'

// Mock the store
jest.mock('@/store/recordingStore')
const mockUseRecordingStore = useRecordingStore as jest.MockedFunction<typeof useRecordingStore>

describe('ErrorDisplay', () => {
  const defaultStoreState = {
    isRecording: false,
    transcript: '',
    error: null,
    isProcessing: false,
    hasPermission: null,
    recordings: [],
    currentRecording: null,
    status: 'idle' as const,
    setRecording: jest.fn(),
    setTranscript: jest.fn(),
    setError: jest.fn(),
    setProcessing: jest.fn(),
    setPermission: jest.fn(),
    setStatus: jest.fn(),
    addRecording: jest.fn(),
    clearRecordings: jest.fn(),
    clearError: jest.fn(),
    reset: jest.fn(),
  }

  beforeEach(() => {
    mockUseRecordingStore.mockReturnValue(defaultStoreState)
  })

  it('renders nothing when no error', () => {
    const { container } = render(<ErrorDisplay />)
    expect(container.firstChild).toBeNull()
  })

  it('displays error message when error exists', () => {
    const errorMessage = 'Microphone access denied'
    mockUseRecordingStore.mockReturnValue({
      ...defaultStoreState,
      error: errorMessage,
    })

    render(<ErrorDisplay />)
    expect(screen.getByText('Recording Error')).toBeInTheDocument()
    expect(screen.getByText(errorMessage)).toBeInTheDocument()
  })

  it('calls clearError when dismiss button is clicked', () => {
    const mockClearError = jest.fn()
    mockUseRecordingStore.mockReturnValue({
      ...defaultStoreState,
      error: 'Test error',
      clearError: mockClearError,
    })

    render(<ErrorDisplay />)
    const dismissButton = screen.getByRole('button', { name: 'Dismiss error' })
    fireEvent.click(dismissButton)
    
    expect(mockClearError).toHaveBeenCalledTimes(1)
  })

  it('applies custom className', () => {
    mockUseRecordingStore.mockReturnValue({
      ...defaultStoreState,
      error: 'Test error',
    })

    const { container } = render(<ErrorDisplay className="custom-class" />)
    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('has proper error styling', () => {
    mockUseRecordingStore.mockReturnValue({
      ...defaultStoreState,
      error: 'Test error',
    })

    const { container } = render(<ErrorDisplay />)
    expect(container.firstChild).toHaveClass('error-message')
  })

  it('displays error icon', () => {
    mockUseRecordingStore.mockReturnValue({
      ...defaultStoreState,
      error: 'Test error',
    })

    render(<ErrorDisplay />)
    const errorContainer = screen.getByText('Recording Error').closest('div')
    const svgIcon = errorContainer?.querySelector('svg')
    expect(svgIcon).toBeInTheDocument()
  })
})
