import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRecordingStore } from '@/store/recordingStore'
import RecordingButton from '../RecordingButton'

// Mock the store
jest.mock('@/store/recordingStore')
const mockUseRecordingStore = useRecordingStore as jest.MockedFunction<typeof useRecordingStore>

// Mock the hook
jest.mock('@/hooks/useVoiceRecording', () => ({
  useVoiceRecording: () => ({
    isRecording: false,
    isProcessing: false,
    status: 'idle',
    toggleRecording: jest.fn(),
    isSupported: true,
  }),
}))

describe('RecordingButton', () => {
  beforeEach(() => {
    mockUseRecordingStore.mockReturnValue({
      isRecording: false,
      transcript: '',
      error: null,
      isProcessing: false,
      hasPermission: null,
      recordings: [],
      currentRecording: null,
      status: 'idle',
      setRecording: jest.fn(),
      setTranscript: jest.fn(),
      setError: jest.fn(),
      setProcessing: jest.fn(),
      setPermission: jest.fn(),
      setStatus: jest.fn(),
      addRecording: jest.fn(),
      clearRecordings: jest.fn(),
      clearError: jest.fn(),
      reset: jest.fn(),
    })
  })

  it('renders the recording button', () => {
    render(<RecordingButton />)
    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveAttribute('aria-label', 'Start recording')
  })

  it('shows microphone icon when idle', () => {
    render(<RecordingButton />)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('idle')
    
    // Check for microphone SVG path
    const microphonePath = screen.getByRole('button').querySelector('path')
    expect(microphonePath).toBeInTheDocument()
  })

  it('shows stop icon when recording', () => {
    // Mock the hook to return recording state
    jest.doMock('@/hooks/useVoiceRecording', () => ({
      useVoiceRecording: () => ({
        isRecording: true,
        isProcessing: false,
        status: 'recording',
        toggleRecording: jest.fn(),
        isSupported: true,
      }),
    }))

    render(<RecordingButton />)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('recording')
    expect(button).toHaveAttribute('aria-label', 'Stop recording')
  })

  it('shows loading spinner when processing', () => {
    jest.doMock('@/hooks/useVoiceRecording', () => ({
      useVoiceRecording: () => ({
        isRecording: false,
        isProcessing: true,
        status: 'processing',
        toggleRecording: jest.fn(),
        isSupported: true,
      }),
    }))

    render(<RecordingButton />)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('processing')
    expect(button).toHaveAttribute('aria-label', 'Processing recording...')
    expect(button).toBeDisabled()
  })

  it('shows not supported message when speech recognition is not available', () => {
    jest.doMock('@/hooks/useVoiceRecording', () => ({
      useVoiceRecording: () => ({
        isRecording: false,
        isProcessing: false,
        status: 'idle',
        toggleRecording: jest.fn(),
        isSupported: false,
      }),
    }))

    render(<RecordingButton />)
    expect(screen.getByText('Not Supported')).toBeInTheDocument()
  })

  it('calls toggleRecording when clicked', () => {
    const mockToggleRecording = jest.fn()
    
    jest.doMock('@/hooks/useVoiceRecording', () => ({
      useVoiceRecording: () => ({
        isRecording: false,
        isProcessing: false,
        status: 'idle',
        toggleRecording: mockToggleRecording,
        isSupported: true,
      }),
    }))

    render(<RecordingButton />)
    const button = screen.getByRole('button')
    fireEvent.click(button)
    
    expect(mockToggleRecording).toHaveBeenCalledTimes(1)
  })

  it('applies custom className', () => {
    render(<RecordingButton className="custom-class" />)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('custom-class')
  })
})
