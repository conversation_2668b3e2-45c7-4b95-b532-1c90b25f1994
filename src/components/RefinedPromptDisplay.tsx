'use client';

import React from 'react';
import { usePromptRefinement } from '@/hooks/usePromptRefinement';

interface RefinedPromptDisplayProps {
  className?: string;
}

const RefinedPromptDisplay: React.FC<RefinedPromptDisplayProps> = ({ className = '' }) => {
  const {
    isRefining,
    refinedPrompt,
    extractedTime,
    needsTimeCllarification,
    followUpQuestion,
    confidence,
    error,
    processingTime,
    clearRefinement,
    clearError,
  } = usePromptRefinement();

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-2">
            <svg className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
            </svg>
            <div>
              <p className="text-sm font-medium text-red-800">Refinement Error</p>
              <p className="text-sm mt-1 text-red-700">{error}</p>
            </div>
          </div>
          <button
            onClick={clearError}
            className="ml-4 text-red-500 hover:text-red-700 transition-colors"
            aria-label="Dismiss error"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
            </svg>
          </button>
        </div>
      </div>
    );
  }

  if (isRefining) {
    return (
      <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
          <div>
            <p className="text-sm font-medium text-blue-800">Refining your prompt...</p>
            <p className="text-sm text-blue-700">AI is processing your voice input</p>
          </div>
        </div>
      </div>
    );
  }

  if (!refinedPrompt) {
    return null;
  }

  return (
    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start justify-between mb-3">
        <h3 className="text-sm font-medium text-green-800 flex items-center">
          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Refined Coaching Prompt
        </h3>
        <button
          onClick={clearRefinement}
          className="text-green-500 hover:text-green-700 transition-colors"
          aria-label="Clear refinement"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
          </svg>
        </button>
      </div>

      {/* Refined Prompt */}
      <div className="bg-white rounded-md p-3 mb-3">
        <p className="text-gray-900 font-medium">{refinedPrompt}</p>
      </div>

      {/* Time Information */}
      {extractedTime && (
        <div className="bg-white rounded-md p-3 mb-3">
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
            </svg>
            <span className="text-sm font-medium text-gray-700">Extracted Time:</span>
            <span className="text-sm text-gray-900">{extractedTime}</span>
          </div>
        </div>
      )}

      {/* Follow-up Question */}
      {needsTimeCllarification && followUpQuestion && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-3">
          <div className="flex items-start space-x-2">
            <svg className="w-4 h-4 text-yellow-500 mt-0.5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p className="text-sm font-medium text-yellow-800">Time Clarification Needed</p>
              <p className="text-sm text-yellow-700 mt-1">{followUpQuestion}</p>
            </div>
          </div>
        </div>
      )}

      {/* Metadata */}
      <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-green-100">
        <div className="flex items-center space-x-4">
          {confidence && (
            <span>Confidence: {Math.round(confidence * 100)}%</span>
          )}
          {processingTime && (
            <span>Processed in {processingTime}ms</span>
          )}
        </div>
        <div className="flex items-center space-x-1">
          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
            <path d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          <span>AI Enhanced</span>
        </div>
      </div>
    </div>
  );
};

export default RefinedPromptDisplay;
