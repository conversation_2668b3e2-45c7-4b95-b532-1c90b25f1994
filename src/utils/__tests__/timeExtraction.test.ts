import { TimeExtractionService } from '../timeExtraction';

describe('TimeExtractionService', () => {
  describe('extractTimeExpressions', () => {
    it('should extract specific times', () => {
      const text = 'I want to exercise at 7:30 AM every day';
      const extractions = TimeExtractionService.extractTimeExpressions(text);
      
      expect(extractions).toHaveLength(2);
      expect(extractions.some(e => e.timeExpression.includes('7:30 AM'))).toBe(true);
      expect(extractions.some(e => e.timeExpression.includes('every day'))).toBe(true);
    });

    it('should extract recurring patterns', () => {
      const text = 'I will read every morning and exercise weekly';
      const extractions = TimeExtractionService.extractTimeExpressions(text);
      
      expect(extractions.length).toBeGreaterThan(0);
      expect(extractions.some(e => e.timeExpression.includes('every morning'))).toBe(true);
      expect(extractions.some(e => e.timeExpression.includes('weekly'))).toBe(true);
    });

    it('should extract relative times', () => {
      const text = 'I want to start tomorrow and continue for 30 days';
      const extractions = TimeExtractionService.extractTimeExpressions(text);
      
      expect(extractions.length).toBeGreaterThan(0);
      expect(extractions.some(e => e.timeExpression.includes('tomorrow'))).toBe(true);
    });

    it('should handle empty text', () => {
      const extractions = TimeExtractionService.extractTimeExpressions('');
      expect(extractions).toHaveLength(0);
    });

    it('should handle text with no time expressions', () => {
      const text = 'I want to be more productive and focused';
      const extractions = TimeExtractionService.extractTimeExpressions(text);
      expect(extractions).toHaveLength(0);
    });
  });

  describe('getBestTimeExtraction', () => {
    it('should return the highest confidence extraction', () => {
      const text = 'I want to exercise at 7 AM every day';
      const best = TimeExtractionService.getBestTimeExtraction(text);
      
      expect(best).toBeTruthy();
      expect(best?.confidence).toBeGreaterThan(0);
    });

    it('should return null for text with no time expressions', () => {
      const text = 'I want to be more productive';
      const best = TimeExtractionService.getBestTimeExtraction(text);
      
      expect(best).toBeNull();
    });
  });

  describe('normalizeTimeExpression', () => {
    it('should normalize specific times to 24-hour format', () => {
      const normalized = TimeExtractionService.normalizeTimeExpression('7:30 AM', 'specific');
      expect(normalized).toBe('07:30');
    });

    it('should normalize PM times correctly', () => {
      const normalized = TimeExtractionService.normalizeTimeExpression('2:30 PM', 'specific');
      expect(normalized).toBe('14:30');
    });

    it('should normalize recurring patterns', () => {
      const normalized = TimeExtractionService.normalizeTimeExpression('every day', 'recurring');
      expect(normalized).toBe('daily');
    });

    it('should normalize relative times', () => {
      const normalized = TimeExtractionService.normalizeTimeExpression('tomorrow', 'relative');
      expect(normalized).toBe('tomorrow');
    });
  });

  describe('needsTimeCllarification', () => {
    it('should return true for empty extractions', () => {
      const needsClarification = TimeExtractionService.needsTimeCllarification([]);
      expect(needsClarification).toBe(true);
    });

    it('should return true for low confidence extractions', () => {
      const extractions = [{
        timeExpression: 'sometime',
        normalizedTime: null,
        timeType: 'unclear' as const,
        confidence: 0.3,
      }];
      
      const needsClarification = TimeExtractionService.needsTimeCllarification(extractions);
      expect(needsClarification).toBe(true);
    });

    it('should return false for high confidence extractions', () => {
      const extractions = [{
        timeExpression: '7:30 AM',
        normalizedTime: '07:30',
        timeType: 'specific' as const,
        confidence: 0.9,
      }];
      
      const needsClarification = TimeExtractionService.needsTimeCllarification(extractions);
      expect(needsClarification).toBe(false);
    });
  });

  describe('generateTimeCllarificationQuestion', () => {
    it('should generate appropriate question for empty extractions', () => {
      const question = TimeExtractionService.generateTimeCllarificationQuestion([]);
      expect(question).toContain('When would you like');
    });

    it('should generate appropriate question for unclear extractions', () => {
      const extractions = [{
        timeExpression: 'sometime soon',
        normalizedTime: null,
        timeType: 'recurring' as const,
        confidence: 0.5,
      }];
      
      const question = TimeExtractionService.generateTimeCllarificationQuestion(extractions);
      expect(question).toContain('sometime soon');
    });
  });
});
