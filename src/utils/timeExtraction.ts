import { TimeExtraction } from '@/types/refinement';

// Time patterns for natural language processing
const TIME_PATTERNS = {
  // Specific times
  specific: [
    /\b(\d{1,2}):(\d{2})\s*(am|pm|AM|PM)?\b/g,
    /\b(\d{1,2})\s*(am|pm|AM|PM)\b/g,
    /\bat\s+(\d{1,2}):?(\d{2})?\s*(am|pm|AM|PM)?\b/gi,
  ],
  
  // Recurring patterns
  recurring: [
    /\bevery\s+(day|morning|evening|night|week|month)\b/gi,
    /\bdaily\b/gi,
    /\bweekly\b/gi,
    /\bmonthly\b/gi,
    /\bon\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday)s?\b/gi,
    /\bevery\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b/gi,
  ],
  
  // Relative times
  relative: [
    /\bin\s+(\d+)\s+(minutes?|hours?|days?|weeks?|months?)\b/gi,
    /\bafter\s+(\d+)\s+(minutes?|hours?|days?)\b/gi,
    /\bnext\s+(week|month|year|monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b/gi,
    /\btomorrow\b/gi,
    /\btoday\b/gi,
    /\bthis\s+(morning|afternoon|evening|night|week|month)\b/gi,
  ],
  
  // Time of day
  timeOfDay: [
    /\bin\s+the\s+(morning|afternoon|evening|night)\b/gi,
    /\b(morning|afternoon|evening|night)\b/gi,
    /\bearly\s+(morning|afternoon|evening)\b/gi,
    /\blate\s+(morning|afternoon|evening|night)\b/gi,
  ],
};

export class TimeExtractionService {
  /**
   * Extract time expressions from natural language text
   */
  static extractTimeExpressions(text: string): TimeExtraction[] {
    const extractions: TimeExtraction[] = [];
    
    // Extract specific times
    this.extractSpecificTimes(text, extractions);
    
    // Extract recurring patterns
    this.extractRecurringPatterns(text, extractions);
    
    // Extract relative times
    this.extractRelativeTimes(text, extractions);
    
    // Extract time of day references
    this.extractTimeOfDay(text, extractions);
    
    return extractions.sort((a, b) => b.confidence - a.confidence);
  }
  
  /**
   * Get the best time extraction from text
   */
  static getBestTimeExtraction(text: string): TimeExtraction | null {
    const extractions = this.extractTimeExpressions(text);
    return extractions.length > 0 ? extractions[0] : null;
  }
  
  /**
   * Normalize time expression to a standard format
   */
  static normalizeTimeExpression(timeExpression: string, timeType: string): string | null {
    const expr = timeExpression.toLowerCase().trim();
    
    switch (timeType) {
      case 'specific':
        return this.normalizeSpecificTime(expr);
      case 'recurring':
        return this.normalizeRecurringTime(expr);
      case 'relative':
        return this.normalizeRelativeTime(expr);
      case 'timeOfDay':
        return this.normalizeTimeOfDay(expr);
      default:
        return null;
    }
  }
  
  private static extractSpecificTimes(text: string, extractions: TimeExtraction[]) {
    TIME_PATTERNS.specific.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const timeExpression = match[0];
        const normalized = this.normalizeSpecificTime(timeExpression);
        
        extractions.push({
          timeExpression,
          normalizedTime: normalized,
          timeType: 'specific',
          confidence: 0.9,
        });
      }
    });
  }
  
  private static extractRecurringPatterns(text: string, extractions: TimeExtraction[]) {
    TIME_PATTERNS.recurring.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const timeExpression = match[0];
        const normalized = this.normalizeRecurringTime(timeExpression);
        
        extractions.push({
          timeExpression,
          normalizedTime: normalized,
          timeType: 'recurring',
          confidence: 0.8,
        });
      }
    });
  }
  
  private static extractRelativeTimes(text: string, extractions: TimeExtraction[]) {
    TIME_PATTERNS.relative.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const timeExpression = match[0];
        const normalized = this.normalizeRelativeTime(timeExpression);
        
        extractions.push({
          timeExpression,
          normalizedTime: normalized,
          timeType: 'relative',
          confidence: 0.7,
        });
      }
    });
  }
  
  private static extractTimeOfDay(text: string, extractions: TimeExtraction[]) {
    TIME_PATTERNS.timeOfDay.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const timeExpression = match[0];
        const normalized = this.normalizeTimeOfDay(timeExpression);
        
        extractions.push({
          timeExpression,
          normalizedTime: normalized,
          timeType: 'recurring',
          confidence: 0.6,
        });
      }
    });
  }
  
  private static normalizeSpecificTime(timeExpr: string): string {
    // Convert to 24-hour format
    const timeMatch = timeExpr.match(/(\d{1,2}):?(\d{2})?\s*(am|pm)?/i);
    if (!timeMatch) return timeExpr;
    
    let hours = parseInt(timeMatch[1]);
    const minutes = timeMatch[2] ? parseInt(timeMatch[2]) : 0;
    const period = timeMatch[3]?.toLowerCase();
    
    if (period === 'pm' && hours !== 12) hours += 12;
    if (period === 'am' && hours === 12) hours = 0;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  }
  
  private static normalizeRecurringTime(timeExpr: string): string {
    const expr = timeExpr.toLowerCase();
    
    if (expr.includes('daily') || expr.includes('every day')) return 'daily';
    if (expr.includes('weekly') || expr.includes('every week')) return 'weekly';
    if (expr.includes('monthly') || expr.includes('every month')) return 'monthly';
    if (expr.includes('morning')) return 'daily-morning';
    if (expr.includes('evening')) return 'daily-evening';
    if (expr.includes('night')) return 'daily-night';
    
    // Days of week
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    for (const day of days) {
      if (expr.includes(day)) return `weekly-${day}`;
    }
    
    return timeExpr;
  }
  
  private static normalizeRelativeTime(timeExpr: string): string {
    const expr = timeExpr.toLowerCase();
    
    if (expr.includes('tomorrow')) return 'tomorrow';
    if (expr.includes('today')) return 'today';
    if (expr.includes('next week')) return 'next-week';
    if (expr.includes('next month')) return 'next-month';
    if (expr.includes('this morning')) return 'today-morning';
    if (expr.includes('this evening')) return 'today-evening';
    
    // Extract numeric relative times
    const numMatch = expr.match(/(\d+)\s+(minutes?|hours?|days?|weeks?|months?)/);
    if (numMatch) {
      const num = numMatch[1];
      const unit = numMatch[2].replace(/s$/, ''); // Remove plural
      return `+${num}-${unit}`;
    }
    
    return timeExpr;
  }
  
  private static normalizeTimeOfDay(timeExpr: string): string {
    const expr = timeExpr.toLowerCase();
    
    if (expr.includes('morning')) return 'morning';
    if (expr.includes('afternoon')) return 'afternoon';
    if (expr.includes('evening')) return 'evening';
    if (expr.includes('night')) return 'night';
    
    return timeExpr;
  }
  
  /**
   * Check if the extracted time needs clarification
   */
  static needsTimeCllarification(extractions: TimeExtraction[]): boolean {
    if (extractions.length === 0) return true;
    
    const bestExtraction = extractions[0];
    return bestExtraction.confidence < 0.7 || bestExtraction.timeType === 'unclear';
  }
  
  /**
   * Generate a follow-up question for time clarification
   */
  static generateTimeCllarificationQuestion(extractions: TimeExtraction[]): string {
    if (extractions.length === 0) {
      return "When would you like to work on this goal? Please specify a time or frequency.";
    }
    
    const bestExtraction = extractions[0];
    
    if (bestExtraction.timeType === 'recurring' && bestExtraction.confidence < 0.8) {
      return `I heard "${bestExtraction.timeExpression}". Could you clarify the specific time or frequency?`;
    }
    
    if (bestExtraction.timeType === 'relative') {
      return `You mentioned "${bestExtraction.timeExpression}". Would you like to set a specific recurring schedule?`;
    }
    
    return `I detected "${bestExtraction.timeExpression}". Is this the correct time for your goal?`;
  }
}
