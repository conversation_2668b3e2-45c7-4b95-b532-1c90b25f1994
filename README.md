# Coach Me - Voice Recording MVP

A modern voice recording application built with Next.js 14, React 18, TypeScript, and Tailwind CSS.

## Phase 1: Voice Recording Infrastructure ✅

This implementation includes:

- **Clean, minimalist recording interface** with a single tap-to-record button
- **Visual feedback** with pulsing animation during recording
- **Audio recording** using Web Speech API with real-time transcription
- **Microphone permission handling** with user-friendly prompts
- **Error handling** for various recording scenarios
- **Responsive design** optimized for mobile browsers
- **State management** using Zustand for clean, predictable state updates

## Features

### ✅ Implemented
- Tap-to-record functionality with visual feedback
- Real-time speech transcription
- Microphone permission management
- Error handling and user feedback
- Mobile-responsive design
- Browser compatibility detection
- Recording state management
- Audio blob creation for future processing

### 🎯 Core Components
- `RecordingButton` - Main recording interface with visual states
- `TranscriptDisplay` - Real-time transcript with word/character counts
- `ErrorDisplay` - User-friendly error messages
- `useVoiceRecording` - Custom hook for recording logic
- `recordingStore` - Zustand store for state management

## Tech Stack

- **Frontend**: Next.js 14 + React 18 + TypeScript
- **Styling**: Tailwind CSS with custom animations
- **State Management**: Zustand
- **Audio Processing**: Web Speech API (browser native)
- **Development**: ESLint + TypeScript strict mode

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## Browser Support

- ✅ Chrome (recommended)
- ✅ Edge
- ✅ Safari
- ❌ Firefox (limited Web Speech API support)

## Usage

1. Allow microphone access when prompted
2. Tap the microphone button to start recording
3. Speak clearly into your device's microphone
4. Watch real-time transcription appear
5. Tap the button again to stop recording

## Project Structure

```
src/
├── app/
│   ├── layout.tsx          # Root layout with Tailwind
│   ├── page.tsx            # Main application page
│   └── globals.css         # Global styles and animations
├── components/
│   ├── RecordingButton.tsx # Main recording interface
│   ├── TranscriptDisplay.tsx # Transcript display
│   └── ErrorDisplay.tsx    # Error handling UI
├── hooks/
│   └── useVoiceRecording.ts # Voice recording logic
├── store/
│   └── recordingStore.ts   # Zustand state management
└── types/
    └── audio.ts            # TypeScript interfaces
```

## Next Steps (Future Phases)

- AI integration with OpenAI API
- Audio playback functionality
- Recording history and management
- Advanced transcription features
- Mobile app deployment with Capacitor

## Development Notes

- Uses Web Speech API for browser-native speech recognition
- Implements proper TypeScript types for speech recognition
- Handles microphone permissions gracefully
- Responsive design works on mobile browsers
- Clean error handling for various edge cases
