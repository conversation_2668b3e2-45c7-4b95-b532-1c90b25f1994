# Coach Me - Voice Recording MVP

A modern voice recording application built with Next.js 14, React 18, TypeScript, and Tailwind CSS.

## Phase 1: Voice Recording Infrastructure ✅

This implementation includes:

- **Clean, minimalist recording interface** with a single tap-to-record button
- **Visual feedback** with pulsing animation during recording
- **Audio recording** using Web Speech API with real-time transcription
- **Microphone permission handling** with user-friendly prompts
- **Error handling** for various recording scenarios
- **Responsive design** optimized for mobile browsers
- **State management** using Zustand for clean, predictable state updates

## Phase 2: AI Prompt Refinement Engine ✅

This implementation includes:

- **OpenAI GPT-4 integration** for intelligent prompt refinement
- **Natural language time extraction** with pattern recognition
- **NestJS backend API** with proper validation and error handling
- **Automatic prompt refinement** when recording stops
- **Time clarification system** for ambiguous time expressions
- **Multi-language support** (English/Hebrew)
- **Coaching context awareness** for personalized refinements
- **Real-time processing** with confidence scoring

## Features

### ✅ Phase 1 - Voice Recording
- Tap-to-record functionality with visual feedback
- Real-time speech transcription
- Microphone permission management
- Error handling and user feedback
- Mobile-responsive design
- Browser compatibility detection
- Recording state management
- Audio blob creation for future processing

### ✅ Phase 2 - AI Prompt Refinement
- OpenAI GPT-4 integration for prompt refinement
- Natural language time extraction and parsing
- NestJS backend with TypeScript
- Automatic refinement after recording
- Time clarification questions
- Confidence scoring and validation
- Multi-language support (EN/HE)
- RESTful API with proper error handling

### 🎯 Core Components

**Frontend:**
- `RecordingButton` - Main recording interface with visual states
- `TranscriptDisplay` - Real-time transcript with word/character counts
- `RefinedPromptDisplay` - AI-enhanced prompt display with metadata
- `ErrorDisplay` - User-friendly error messages
- `useVoiceRecording` - Custom hook for recording logic
- `usePromptRefinement` - Custom hook for AI refinement
- `recordingStore` - Zustand store for state management

**Backend:**
- `RefinementController` - REST API endpoints for prompt refinement
- `RefinementService` - Core business logic and OpenAI integration
- `OpenAIService` - OpenAI API wrapper with error handling
- `TimeExtractionService` - Natural language time parsing
- `RefinementRequestDto` - Request/response validation

## Tech Stack

- **Frontend**: Next.js 14 + React 18 + TypeScript
- **Backend**: NestJS + TypeScript + OpenAI API
- **Styling**: Tailwind CSS with custom animations
- **State Management**: Zustand
- **Audio Processing**: Web Speech API (browser native)
- **AI Integration**: OpenAI GPT-4 for prompt refinement
- **Testing**: Jest + React Testing Library + Playwright
- **Development**: ESLint + TypeScript strict mode

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Add your OpenAI API key to .env
   ```

3. **Run full development environment**:
   ```bash
   npm run dev:full
   ```
   This starts both frontend (port 3000) and backend (port 3001)

4. **Or run separately**:
   ```bash
   # Frontend only
   npm run dev

   # Backend only
   npm run dev:backend
   ```

5. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## Browser Support

- ✅ Chrome (recommended)
- ✅ Edge
- ✅ Safari
- ❌ Firefox (limited Web Speech API support)

## Usage

1. Allow microphone access when prompted
2. Tap the microphone button to start recording
3. Speak clearly into your device's microphone
4. Watch real-time transcription appear
5. Tap the button again to stop recording

## Project Structure

```
src/
├── app/
│   ├── layout.tsx          # Root layout with Tailwind
│   ├── page.tsx            # Main application page
│   └── globals.css         # Global styles and animations
├── components/
│   ├── RecordingButton.tsx # Main recording interface
│   ├── TranscriptDisplay.tsx # Transcript display
│   └── ErrorDisplay.tsx    # Error handling UI
├── hooks/
│   └── useVoiceRecording.ts # Voice recording logic
├── store/
│   └── recordingStore.ts   # Zustand state management
└── types/
    └── audio.ts            # TypeScript interfaces
```

## Next Steps (Future Phases)

- AI integration with OpenAI API
- Audio playback functionality
- Recording history and management
- Advanced transcription features
- Mobile app deployment with Capacitor

## Development Notes

- Uses Web Speech API for browser-native speech recognition
- Implements proper TypeScript types for speech recognition
- Handles microphone permissions gracefully
- Responsive design works on mobile browsers
- Clean error handling for various edge cases
